<?php

namespace App\Admin\Actions;


use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Models\User as UserModel;
use App\Admin\Repositories\User;
use Dcat\Admin\Grid;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;


class ShowUserBankCard extends Action
{

    /**
     * @return string
     */
	protected $title = '银行卡';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */

    protected $modalid = 'show-current-user';



    public function handle(Request $request)
    {

       $tabls =  Grid::make(new User(), function (Grid $grid) {

            $banme  = $this->payload['key'] ?? null;

            $grid->model()->where('bname', $banme)->orderByDesc('created_at');
            $grid->column('uname','账号');
            $grid->column('bname','持卡人姓名');
            $grid->column('msg','备注');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();
            });
           
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->column('msg','备注')->editable();

            // 编辑成功后刷新页面
            $grid->column('msg','备注')->editable(true);
            $grid->disableActions();

        });

        return $this->response()->success('查询成功')->html($tabls);
    }
 
    protected function handleHtmlResponse()
    {

       return <<<'JS'
       function (target, html, data) {

            var $modal = $(target.data('target'));

            $modal.find('.modal-body').html(html);
            $modal.modal('show')
       } 
       JS;
    }


    public function html()
    {
        // 按钮的html
        $html = parent::html();

        return <<<HTML
           {$html}        
           <div class="modal fade" id="{$this->modalid}" tabindex="-1" role="dialog">
           <div class="modal-dialog modal-lg" role="document">
               <div class="modal-content">
               <div class="modal-header">
                   <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                   <h4 class="modal-title">{$this->title()}</h4>
               </div>
               <div class="modal-body"></div>
               </div>
           </div>
           </div>
        HTML;
    }


    protected function setupHtmlAttributes()
   {
       // 添加class
       $this->addHtmlClass('btn btn-primary');

       // 保存弹窗的ID
       $this->setHtmlAttribute('data-target', '#'.$this->modalid);

       parent::setupHtmlAttributes();
   }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        // return ['Confirm?', 'contents'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
