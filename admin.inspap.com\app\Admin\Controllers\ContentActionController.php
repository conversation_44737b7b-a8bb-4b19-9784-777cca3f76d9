<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ContentAction;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
class ContentActionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ContentAction(), function (Grid $grid) {

            Tools::viewroles($grid);  //视图权限筛选
            $grid->withBorder();
            $grid->column('title');
            $grid->column('body');

            $grid->column('image')->display(function () {
              
                return '<img src="'.env('APP_URL').'/upload'.'/'.$this->image.'" style="width: 100px;"';
                
            });
            $grid->column('class')->display(function () {
              
                $lang = Tools::language();
                return $lang[$this->class];
                
            });
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {
               // $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });
        
   
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('title')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ContentAction(), function (Show $show) {
            $show->field('id');
            $show->field('title');
            $show->field('body');
            $show->field('image');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ContentAction(), function (Form $form) {

            $admin  = Auth::guard('admin')->user(); 

            Admin::style(
                <<<CSS
                #layui-layer1 {
                    width: auto!important;
                }
                CSS            
            );

            if($form->isCreating()){
                $admin   = Auth::guard('admin')->user(); 
                $form->hidden('staff_id');
                $form->hidden('team_id');
                $form->staff_id = $admin->id;
                $form->team_id = $admin->team_id;
                $form->select('class')->options(Tools::language())
                ->default('VN',true);

            }
            $form->text('title')->required();
            $form->editor('body')->required();
            $form->image('image')->required();
           
     
        });
    }
}
