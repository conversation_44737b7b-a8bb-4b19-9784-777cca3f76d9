<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Admin\Forms\Setting;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Models\UsersRecharge as sRecharge ;
use App\Models\AdminUser;
use App\Admin\Controllers\CountController as IndexCount;
/**数据中心各类统计 */


class CountCenterController extends Controller
{
    public function index(Request $request,$action)
    {

        if($action=='staff'){
           return $this->count_staff($request);
        }
        else if($action=='team'){

           return $this->count_team($request);
        }
  
    }



    //员工统计
    public function count_staff($request){

       //获取所属员工

       $where = []; 
       $admin = Auth::guard('admin')->user(); 
       $roles = Admin::user()->roles; //获取权限分组
       
       if($roles[0]->slug == 'staff'){   //如果是员工角色 只看自己所属的用户

           $where = [
               ['id',$admin->id]
           ];

           $user =  AdminUser::where($where)->get();  //得到员工
       }

       if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团

           $where = [
               ['team_id',$admin->team_id]
           ];
           $user =  AdminUser::where($where)->get();  //得到员工
       }

       if($roles[0]->slug == 'administrator'){   //如果是超级管理员 排除掉团长

          $team_ =  DB::table('admin_team')->get();

          $team_user = [];

          for($t=0;$t<count($team_);$t++){
             $team_user[$t] = $team_[$t]->uid;
          }

          array_push($team_user,1);

          $user =  AdminUser::whereNotIn('id',$team_user)->get();  //排除团长后得到员工


        }

        

       //组合 数据  

       $datacount = new IndexCount();
       $user_teamid= [];
       $user_id   = [];
       if($user){
          for($i=0;$i<count($user);$i++){

                $team_name =  DB::table('admin_team')->where('id',$user[$i]->team_id)->first();
                
                $code_      = DB::table('staff_code')->where('uid',$user[$i]->id)->first();
                $poplecount = DB::table('admin_users')->select(DB::raw('count(username) as count'))->where('team_id',$user[$i]->team_id)->first();
                $code = '';
                if($code_){
                   $code = $code_;
                }

                $user_teamid[$i] = $user[$i]->team_id;
                $user_id[$i] = $user[$i]->id;
                $user[$i]->datacount = $datacount->ex_staff($request,$user[$i]->id);
                $user[$i]->team_name = $team_name->username ?? 'null';
                $user[$i]->code      = $code_->code ?? 'null';
                $user[$i]->popelecount = $poplecount->count;
          }
       }



       $retun_msg = [
           'data' => $user,
           'msg'  =>'数据获取成功！',
           'status' => 200,
       ];
       return $retun_msg;


    }

    //团队统计
    public function count_team($request){

        //获取所属员工
 
        $where = []; 
        $admin = Auth::guard('admin')->user(); 
        $roles = Admin::user()->roles; //获取权限分组
        
        if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团
 

            $team_user =  AdminUser::where('id',$admin->id)->get();  //得到团长自己
            
        }
 
        if($roles[0]->slug == 'administrator'){   //如果是超级管理员 排除掉团长
 
           $team_ =  DB::table('admin_team')->get();
 
           $team_e = [];
 
           for($t=0;$t<count($team_);$t++){
              $team_e[$t] = $team_[$t]->uid;
           }
 
 
           $team_user =  AdminUser::whereIn('id',$team_e)->get();  //得到全部团长
 
 
         }
 
         
 
        //组合 数据  
 
        $datacount = new IndexCount();
        $team_data = [];
        if($team_user){
           for($i=0;$i<count($team_user);$i++){

                $user      =  AdminUser::where('team_id',$team_user[$i]->team_id)->get();  //得到团队所属员工
                $team_user[$i]->datacount = $datacount->ex_team($request,$team_user[$i]->team_id);
                $team_user[$i]->team_count = count($user);

           }
        }
 
        $retun_msg = [
            'data' => $team_user,
            'msg'  =>'数据获取成功！',
            'status' => 200,
        ];
        return $retun_msg;
 
 
     }
}
