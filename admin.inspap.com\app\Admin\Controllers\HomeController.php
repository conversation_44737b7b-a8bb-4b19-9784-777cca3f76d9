<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Admin\Forms\Setting;
use App\Admin\Forms\ServerReatart;
use App\Admin\Forms\IndexDateForm;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;
use App\Http\Controllers\Lang;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Tools;
class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header(Lang::translate('平台首页'))
            ->body(view('index'));
           
    }
    
   public function server_restart(Content $content)
    {
        return $content
        ->title(Lang::translate('产品服务'))
        ->body(new Card(new ServerReatart()));

           
    }

    public function welecome(Content $content){
        return $content
        ->title(Lang::translate('站点设置'))
        ->body(new Card(new Setting()));
    }

    public function CountCenter(Content $content,$action){

        if($action=='staff'){
            return $content
            ->title(Lang::translate('数据中心'))
            ->description($action)
            ->body(view('CountCenter'));
        }
        if($action=='team'){
            return $content
            ->title(Lang::translate('数据中心'))
            ->description($action)
            ->body(view('CountCenterTeam'));
        }
    }

    // 语言切换方法
    public function changeLocale($lang)
    {
       
        $file_url = '../config/app.php';
       
        $now_lan = app()->getLocale();
        $contents = file_get_contents($file_url);
        $contents = str_replace($now_lan,$lang,$contents);
        file_put_contents($file_url,$contents);
   
        return Tools::Returnajax(true,'成功',200);
      
    }


}
