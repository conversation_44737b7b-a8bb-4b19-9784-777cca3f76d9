<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ProductOrdering;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
class ProductOrderingController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ProductOrdering(), function (Grid $grid) {

            
            // 启用表格异步渲染功能
            $grid->async();

            $grid->disableCreateButton(); //隐藏新增按钮

            $grid->model()->where('status',1); //筛选 没有开奖的列表
            $grid->column('oid');
            $grid->column('uname');
            $grid->column('pname');
            $grid->column('issue');
           
          
            $grid->column('pair_data')->display(function ($res) {

                $productType = Tools::productType();

                return $productType[$this->pair_data];
  

            });
            $grid->column('status')->display(function ($res) {

                $productStatus = Tools::productStatus();

                return $productStatus[$this->status];

            });
            $grid->column('quantity');
            $grid->column('quantity_price');
            $grid->column('lottery_price');
            $grid->column('profit');
          
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });

            $grid->filter(function (Grid\Filter $filter) {
                                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('oid')->width(2);
                $filter->equal('uname')->width(1);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ProductOrdering(), function (Show $show) {
            $show->field('id');
            $show->field('oid');
            $show->field('uname');
            $show->field('msg');

            $show->field('pair_data');
            $show->field('status');
            $show->field('quantity');
            $show->field('quantity_price');
            $show->field('lottery_price');
            $show->field('profit');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ProductOrdering(), function (Form $form) {
          
         
            if($form->isEditing()){  //编辑

                $productType = Tools::productType();
                $form->radio('pair_data')->options([
                    '1' => $productType[1], 
                    '2' => $productType[2], 
                    '3' => $productType[3], 
                    '4' => $productType[4], 
                    
                ])->default('1');

            }
       
        });
    }


}
