<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ProductTmp;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Product;
use App\Http\Controllers\Tools;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Http\Controllers\Lang;
class ProductTmpController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ProductTmp(), function (Grid $grid) {


            // 启用表格异步渲染功能
            $grid->async();
            $grid->withBorder();
            $grid->model()->orderBy('open_at','ASC');


            $grid->selector(function (Grid\Tools\Selector $selector) {
 
                    $product = Product::get();

                    $list = [];
                    $array = [];
                    for($i=0;$i<count($product);$i++){

       
                        array_push($array,$product[$i]->pname);
                    };

                    foreach($array as $key => $val){
                        $list[$val] = $val;
                    }
                
                 
                    $selector->select('pname',Lang::translate('类型'), $list);
            });

       
            $grid->column('issue');
            $grid->column('pname');
           
            $grid->column('dx')->display(function () {
                $productType = Tools::productType();
                
                return $productType[$this->dx];
                
            });
            $grid->column('sd')->display(function () {
                $productType = Tools::productType();
                
                return $productType[$this->sd];
                
            });
            $grid->column('action_admin');
            $grid->column('open_at')->display(function () {
                $times = date('Y-m-d H:i',$this->open_at);
                
                return $times ;
                
            });
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
             
                $actions->disableView();
                $roles = Admin::user()->roles; //获取权限分组
                if($roles[0]->slug == 'administrator'){   //只有超级管理员可以更改

                    $actions->QuickEdit();    // 启用快速编辑（弹窗）
                }

            });

            $grid->filter(function (Grid\Filter $filter) {
                                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('issue')->width(3);
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ProductTmp(), function (Show $show) {
            $show->field('id');
            $show->field('issue');
            $show->field('pname');
            $show->field('class');
            $show->field('dx');
            $show->field('sd');
            $show->field('status');
            $show->field('action_admin');
            $show->field('open_at');
            $show->field('ban_user');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ProductTmp(), function (Form $form) {

            $productType = Tools::productType();

            if($form->isCreating()){  //新增
               
 
                $form->hidden('open_at')->value(date('Y-m-d H:i:s',time()));
                $products = Product::get();

                //组合下拉框选项
                $option = [];
                for($i=0;$i<count($products);$i++){
                   $option[$products[$i]->pid]= $products[$i]->pname;
                };


                $form->select('pname')->options($option);  //$option 形式 ['pid'=>'pname','pid'=>'pname']
                $form->hidden('pid');
               
                $form->saving(function (Form $form) {

                    $productse = Product::get();

                    $optione = [];
                    for($i=0;$i<count($productse);$i++){
                       $optione[$productse[$i]->pid]= $productse[$i]->pname;
                    };

                    $form->pid   = $form->pname;  //获取选中的VULE 就是PID
                    $form->pname = $optione[$form->pname];  //通过PID获取到Pname
                });



                $form->text('issue');

                $form->radio('dx')->options([
                    '1' => $productType[1], 
                    '2' => $productType[2], 
                  
                    
                ])->default($form->dx);

                $form->radio('sd')->options([
                    '3' => $productType[3], 
                    '4' => $productType[4], 
                  
                    
                ])->default($form->sd);

            }
            if($form->isEditing()){  //编辑


                $form->text('pname')->disable();
                $form->text('issue')->disable();
             
                $form->radio('dx')->options([
                    '1' => $productType[1], 
                    '2' => $productType[2], 
                  
                    
                ])->default($form->dx);

                $form->radio('sd')->options([
                    '3' => $productType[3], 
                    '4' => $productType[4], 
                  
                    
                ])->default($form->sd);
                $form->radio('status',Lang::translate('限制投注'))->options([
                    '1' => Lang::translate('否'), 
                    '2' => Lang::translate('是'), 
                  
                    
                ])->default($form->status);
                $form->html('<span style="color:red">*'.Lang::translate('限制账号请用英文,号隔开').'</span>', $label = '');
                $form->text('ban_user',Lang::translate('限制投注账号'));

            }
        });
    }
}
