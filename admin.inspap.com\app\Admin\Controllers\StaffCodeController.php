<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\StaffCode;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Models\StaffCode as Code;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use App\Http\Controllers\Lang;

class StaffCodeController extends AdminController
{
    protected function grid()
    {
        return Grid::make(new StaffCode(), function (Grid $grid) {
            $grid->withBorder();
            
            // 获取当前用户角色
            $roles = Admin::user()->roles;
            $admin = Auth::guard('admin')->user(); 
            $currentRole = $roles[0]->slug;
            
            // 根据角色设置查看权限
            switch($currentRole) {
                case 'staff':
                    // 员工只能查看自己的邀请码
                    $grid->model()->where('uid', $admin->id);
                    $grid->disableCreateButton();
                    break;
                    
                case 'team':
                    // 团长可以查看团队成员的邀请码
                    $team = DB::table('admin_users')
                        ->where('team_id', $admin->team_id)
                        ->pluck('id')
                        ->toArray();
                    $grid->model()->whereIn('uid', $team);
                    break;
                    
                case 'administrator':
                    // 管理员可以查看所有邀请码
                    break;
                    
                default:
                    // 其他角色无权限查看
                    $grid->model()->where('uid', 0);
                    break;
            }
            
            $grid->column('id')->sortable();
            $grid->column('uid')->display(function ($res) {
                $where = [
                    ['id', $this->uid]
                ];
                $res = DB::table('admin_users')->where($where)->first();
                $name = ($res) ? $res->username : Lang::translate('暂无');
                return $name;
            })->label('primary');

            $grid->column('code')->copyable();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
           
            // 设置操作权限
            $grid->actions(function (Grid\Displayers\Actions $actions) use ($currentRole) {
                // 禁用查看按钮
                $actions->disableView();
                
                // 根据角色设置编辑权限
                if ($currentRole === 'staff') {
                    // 员工禁用所有操作
                    $actions->disableDelete();
                    $actions->disableEdit();
                } else if ($currentRole === 'team' || $currentRole === 'administrator') {
                    // 团长和管理员启用快速编辑
                    $actions->disableEdit();  // 禁用普通编辑
                    $actions->QuickEdit();    // 启用快速编辑
                }
            });

            // 搜索功能
            $grid->filter(function (Grid\Filter $filter) use ($currentRole) {
                $filter->equal('id');
                
                // 管理员和团长可以按邀请码搜索
                if (in_array($currentRole, ['administrator', 'team'])) {
                    $filter->like('code', '邀请码');
                    
                    // 管理员可以按员工ID搜索
                    if ($currentRole === 'administrator') {
                        $filter->equal('uid', '员工ID');
                    }
                }
            });
        });
    }

    protected function form()
    {
        return Form::make(new StaffCode(), function (Form $form) {
            // 获取当前用户角色
            $roles = Admin::user()->roles;
            $currentRole = $roles[0]->slug;
            
            // 只允许管理员和团长编辑
            if (!in_array($currentRole, ['administrator', 'team'])) {
                $form->html('<div class="alert alert-danger">您没有权限执行此操作</div>');
                $form->disableSubmitButton();
                $form->disableResetButton();
                return;
            }
            
            $form->display('id');
            
            // 管理员可以选择任意员工,团长只能选择团队内员工
            if ($currentRole === 'administrator') {
                $form->select('uid', '员工')
                    ->options(DB::table('admin_users')->pluck('username', 'id'))
                    ->required();
            } else {
                $admin = Auth::guard('admin')->user();
                $form->select('uid', '员工')
                    ->options(DB::table('admin_users')
                        ->where('team_id', $admin->team_id)
                        ->pluck('username', 'id'))
                    ->required();
            }
            
            $form->text('code', '邀请码')->required();
            
            // 验证邀请码唯一性
            $form->submitted(function (Form $form) {
                if (Code::where('code', $form->code)
                    ->where('id', '<>', $form->model()->id)
                    ->exists()) {
                    $form->responseValidationMessages('code', Lang::translate('邀请码已存在'));
                }
            });

            $form->confirm(Lang::translate('您确定要提交吗？'));
            
            // 保存前的处理
            $form->saving(function (Form $form) {
                // 如果是编辑模式,验证权限
                if ($form->isEditing()) {
                    $roles = Admin::user()->roles;
                    $admin = Auth::guard('admin')->user();
                    $currentRole = $roles[0]->slug;
                    
                    if ($currentRole === 'team') {
                        // 团长只能编辑团队内的邀请码
                        $staffInTeam = DB::table('admin_users')
                            ->where('team_id', $admin->team_id)
                            ->where('id', $form->model()->uid)
                            ->exists();
                            
                        if (!$staffInTeam) {
                            return $form->response()
                                ->error(Lang::translate('您没有权限编辑此邀请码'));
                        }
                    }
                }
            });
        });
    }
}