<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SystemBank;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Lang;
class SystemBankController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemBank(), function (Grid $grid) {


            $grid->withBorder();
            $grid->enableDialogCreate(); //开启 弹窗新增
            $grid->column('id')->sortable();
            $grid->column('bankname');
            // $grid->column('ico')->display(function () {
              
            //     return '<img src="'.env('APP_URL').'/upload'.'/'.$this->ico.'" style="width: 50px;"';
                
            // });
     
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('bname');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SystemBank(), function (Show $show) {
            $show->field('id');
            $show->field('bankname');
            $show->field('ico');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SystemBank(), function (Form $form) {
            $form->display('id');
            $form->text('bankname')->required();
            // $form->image('ico')->name(function ($file) {
            //     return 'test.'.$file->guessExtension();
            // })->required();
           
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
