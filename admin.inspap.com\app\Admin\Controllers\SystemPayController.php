<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SystemPay;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Http\Controllers\Lang;
use Dcat\Admin\Http\Controllers\AdminController;

class SystemPayController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemPay(), function (Grid $grid) {
            //$grid->disableCreateButton();
            $grid->withBorder();

            $grid->column('id');

        
            $grid->column('payname',Lang::translate('服务商'));

            $grid->column('month_name',Lang::translate('支付名'));

            $grid->column('month_code',Lang::translate('支付代码'));

            $grid->column('status',Lang::translate('状态'))->display(function () {

                    if($this->status==0){
                        return Lang::translate('关闭');

                    }
                    else{
                        return Lang::translate('打开');
                    }


            });
          
            $grid->actions(function (Grid\Displayers\Actions $actions) {
               // $actions->disableDelete();
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
             

            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SystemPay(), function (Show $show) {
            $show->field('id');
            $show->field('payname');
            $show->field('ico');
            $show->field('status');
            $show->field('month_name');
            $show->field('month_code');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SystemPay(), function (Form $form) {
            $form->text('payname')->required();
       
            $form->text('month_name')->required();
            $form->text('month_code');
           $form->switch('status');

        });
    }
}
