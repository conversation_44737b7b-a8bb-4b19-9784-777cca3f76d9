<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersBillInfo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Lang;
class UsersBillInfoController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersBillInfo(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('uid');
            $grid->column('action');
            $grid->column('price');
            $grid->column('status');
            $grid->column('msg');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersBillInfo(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('action');
            $show->field('price');
            $show->field('status');
            $show->field('msg');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersBillInfo(), function (Form $form) {
            $form->display('id');
            $form->text('uid');
            $form->text('action');
            $form->text('price');
            $form->text('status');
            $form->text('msg');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
