<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersDm;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use Illuminate\Http\Request;
use App\Models\UsersDm as UsersDmModles;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;
use App\Http\Controllers\Lang;
use Illuminate\Support\Facades\Auth;
class UsersDmController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */

     
    protected function grid()
    {
        return Grid::make(new UsersDm(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('uid');
            $grid->column('outdm');
            $grid->column('nowdm');
            $grid->column('status');
            $grid->column('msg');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersDm(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('outdm');
            $show->field('nowdm');
            $show->field('status');
            $show->field('msg');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersDm(), function (Form $form) {
            $form->display('id');
            $form->text('uid');
            $form->text('outdm');
            $form->text('nowdm');
            $form->text('status');
            $form->text('msg');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public function dm(Request $request){

       $action =  $request->input('action');
        if($action=='select'){

            $where = [
                ['uname',$request->input('uname')]
            ];
    
            $res  =  UsersDmModles::where($where)->first();
            $re   =  DB::table('users')->where($where)->first();
           
            $roles = Admin::user()->roles; //获取权限分组

            $admin  = Auth::guard('admin')->user(); 


            if($res){

                //员工权限
                if($roles[0]->slug=='staff'){
                
                    if($re->staff_id!=$admin->id){

                        return Tools::Returnajax(null,Lang::translate('无权操作此用户'),403);
                    }
                }

                    //团队权限
                if($roles[0]->slug=='team'){
                    
                        if($re->team_id!=$admin->team_id){

                            return Tools::Returnajax(null,Lang::translate('无权操作此用户'),403);
                        }
                }
           


                $res->price = $re->price;  //获取用户账户金额  

                return Tools::Returnajax($res,Lang::translate('查询成功'));
            }
            else{
                return Tools::Returnajax(null,Lang::translate('未查询到该用户'),404);
            }
            


        }
        else{
            return Tools::Returnajax(null,Lang::translate('Action参数错误'),404);
        }

    
    }

}
