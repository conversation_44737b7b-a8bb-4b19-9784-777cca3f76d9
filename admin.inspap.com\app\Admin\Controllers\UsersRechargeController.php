<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersRecharge;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use App\Http\Controllers\Lang;
class UsersRechargeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersRecharge(), function (Grid $grid) {

           // 复写了数据仓库
           $grid->model()->orderBy('id','desc'); //正式用户
            $grid->disableCreateButton();
            $grid->withBorder();
            $grid->column('id')->sortable();
            $grid->column('order_no');
            $grid->column('uname');
            $grid->column('uid')->display(function () {
                
                $re = User::where('uid',$this->uid)->first();
                return $re->bname;
                
            });
           
            $grid->column('num');


            $grid->column('action')->display(function () {
                
                $actionType = Tools::actionType(); //操作方式

                return $actionType[$this->action];
                
            });
            $grid->column('price');
            $grid->column('amount');

            $grid->column('status')->display(function () {
                if($this->status==1){
                    return Lang::translate('正常');
                }
 
                else{
                    return Lang::translate('未知');
                }
                
            });
            $grid->column('msg')->display(function () {
                if($this->action==1 || $this->action==2){
                    return '<span class="color-succes">'.Lang::translate('支付成功').'<span>';
                }
                else if($this->status==0){
                    return '<span class="color-withg">'.Lang::translate('待支付').'<span>';
                }
                else if($this->status==1){
                    return '<span class="color-succes">'.Lang::translate('支付成功').'<span>';
                }
               
                else{
                    return '<span class="color-red">'.Lang::translate('支付失败').'<span>';
                }
                
            });

      

            $grid->column('action_user');
           
            $grid->column('created_at');
        
          
                 
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                //$actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersRecharge(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('action');
            $show->field('price');
            $show->field('status');
            $show->field('msg');
            $show->field('action_user');
            $show->field('amount');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersRecharge(), function (Form $form) {
            $form->display('id');
            $form->text('uid');
            $form->text('action');
            $form->text('price');
            $form->text('status');
            $form->text('msg');
            $form->text('action_user');
            $form->text('amount');
            $form->hidden('uname');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
