<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersWithdraw;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\UsersBillInfo as BillTable;
use App\Models\User;
use App\Models\UsersWithdraw as Withdraw;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
use App\Http\Controllers\BankCode;
use App\Http\Controllers\PlayCommon;
class UsersWithdrawController extends AdminController
{


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersWithdraw(), function (Grid $grid) {
            
      
            $grid->disableCreateButton();
            $grid->withBorder();
            $grid->column('id');
            $grid->column('order_no');
            $grid->column('uname');
            $grid->column('bname');
            $grid->column('bank_name');
            $grid->column('bank_id');
            $grid->column('price');
            $grid->column('amount');
            $grid->column('status')->display(function(){



                //这里的 0，1，3，4跟Tools的无关
                  if($this->status==0){
                    return '<span class="label create-form'.$this->id.'" style="background:#ffcd18">'.Lang::translate('待审核').'</span>';
                  }
                  else if($this->status==1){
                    return '<span class="label" style="background:#21b978">'.Lang::translate('审核成功').'</span>';
                  }
                  else if($this->status==7){

                        if($this->play_status==0){
                            return '<span class="label" style="background:#ffcd18">'.Lang::translate('NicePay（支付中）').'</span>';
                        }
                        else if($this->play_status==1){
                            return '<span class="label" style="background:#21b978">'.Lang::translate('NicePay代付（成功）').'</span>';
                        }
                        else{
                            return '<span class="label" style="background:#e30000">'.Lang::translate('NicePay代付（失败）').'</span>';
                        }
                    
                 }
                  else if($this->status==4){

                        if($this->play_status==0){
                            return '<span class="label" style="background:#ffcd18">'.Lang::translate('KBIO（支付中）').'</span>';
                        }
                        else if($this->play_status==1){
                            return '<span class="label" style="background:#21b978">'.Lang::translate('KBIO（成功）').'</span>';
                        }
                        else{
                            return '<span class="label" style="background:#e30000">'.Lang::translate('KBIO（失败）').'</span>';
                        }
                    
                  }
                  else if($this->status==8){

                    if($this->play_status==0){
                        return '<span class="label" style="background:#ffcd18">'.Lang::translate('AllPay（支付中）').'</span>';
                    }
                    else if($this->play_status==1){
                        return '<span class="label" style="background:#21b978">'.Lang::translate('AllPay代付（成功）').'</span>';
                    }
                    else{
                        return '<span class="label" style="background:#e30000">'.Lang::translate('AllPay代付（失败）').'</span>';
                    }
                
                 }
                  else{
                    return '<span class="label" style="background:#e30000">'.Lang::translate('审核驳回').'</span>';
                  }


            });
            $grid->column('system_msg');
           
            $grid->column('msg');
            $grid->column('sys_no');
            $grid->column('created_at');
            $grid->column('updated_at');
      
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit(); 
                $actions->disableView();
                $ids =  $actions->row->id;
                UsersWithdrawController::build( $ids);

            });
            
            
          $grid->filter(function (Grid\Filter $filter) {
                                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname')->width(3);
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersWithdraw(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('action');
            $show->field('price');
            $show->field('status');
            $show->field('system_msg');
            $show->field('action_user');
            $show->field('amount');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersWithdraw(), function (Form $form) {
            $roles = Admin::user()->roles; //获取权限分组
            $form->text('price')->disable();
            $form->text('amount')->disable();


            if($roles[0]->slug=='administrator'){

                $form->select('status')->attribute('class', 'play_type')->options([
                    '0' =>Lang::translate('待审核'),
                    '1' =>Lang::translate('审核通过'),
                    '2' =>Lang::translate('审核驳回'),
                    '7' =>Lang::translate('NicePay代付'),
                    '8' =>Lang::translate('AllPay代付'),
                    '4' =>Lang::translate('KBIO代付'),
                   
                ]);

            }
            else{

                $form->select('status')->options([
                    '0' =>Lang::translate('待审核'),
                   // '2' =>Lang::translate('审核驳回')
                ]);
            
            }
           
            $form->text('system_msg')->disable();
            $form->text('msg');

            $form->confirm(Lang::translate('您确定要提交吗？'), $form->code);
            $form->disableResetButton();
        });
    }

    static function build($ids)
    {

        $roles = Admin::user()->roles; //获取权限分组

        if($roles[0]->slug=='administrator'){

            Form::dialog(Lang::translate('提现审核'))
                ->click('.create-form'.$ids.'') // 绑定点击按钮
                ->url(env('APP_URL').'/admin/member/withdraw/'.$ids.'/edit') // 表单页面链接，此参数会被按钮中的 “data-url” 属性替换。。
                ->width('700px') // 指定弹窗宽度，可填写百分比，默认 720px
                ->height('650px') // 指定弹窗高度，可填写百分比，默认 690px
                ->success(
                        <<<JS
                        //更新账单 和 用户表 金额
                        if(response.status){
                        ajax_user_bill($ids); //JS请求 跟下面那个PHP 不是一个事情
                        }
                        
                        // 保存成功之后刷新页面
                        Dcat.reload();
                        JS 
            ); 
        }
     
                 
    }

    //更新账单
    protected function updata_user_bill($ids)
    {
         $Withdraw = Withdraw::find($ids);

         $user     = User::where('uid',$Withdraw->uid)->first();


         if($Withdraw->status==1){  //证明审核通过了

            $admin       = Auth::guard('admin')->user();
            $create_data = [
                'uid'   => $user->uid,
                'uname' =>  $user->uname,
                'action' => 3,  //3表示提现操作
                'price' => 0 - $Withdraw->price,  //被提现的金额
                'amount' => $Withdraw->amount,  //提现前金额
                'direction' => 2, //出账
                'action_user'=>  $admin->username,

            ];

            $path = "./logs";
            if(!file_exists($path)) 
            { 
                //检查是否有该文件夹，如果没有就创建，并给予最高权限 
                mkdir("$path" , 0700); 
            }
            $myfile = fopen("{$path}/log_bill.txt", "a");
            fwrite($myfile, json_encode($create_data));
            fclose($myfile); 

            

            $bill = BillTable::create($create_data);
            Withdraw::where('id',$ids)->update(['play_status' => 1]);

         }
         else if($Withdraw->status==7){  
            //nicepay请求代付
            $api_url = env('APP_URL').'/api/playfastpay?bank_server=nicepay&action=payment';
            $bankname = strtoupper(str_replace(' ', '',$Withdraw->bank_name));
            $bankcode =  BankCode::get_bankcode('nicepay',$bankname);

            if($bankcode!=404){
                $params = array(
                    "acc_code"=> $bankcode,
                    "acc_name"=> $Withdraw->bname,
                    "acc_no"=> $Withdraw->bank_id,
                    "order_amount"=> $Withdraw->price,
                    "order_no"=> $Withdraw->order_no,
                );
        
                file_get_contents($api_url, false, stream_context_create(array(
                    'http' => array(
                        'method' => 'GET',
                        'header' => 'Content-type: application/json',
                        'content' => json_encode($params)
                    )
                )));
           }
           else{  //发起代付失败


                 PlayCommon::play_error($Withdraw->order_no,-1,'bank is not found');
              
           }

         }

         else if($Withdraw->status==4){  

            //kbio请求代付
            $api_url = env('APP_URL').'/api/playfastpay?bank_server=kbio&action=payment';
            $bankname = strtoupper(str_replace(' ', '',$Withdraw->bank_name));
            $bankcode =  BankCode::get_bankcode('kbio',$bankname);

            if($bankcode!=404){
                    $params = array(
                        "payout_method"=>$bankcode,
                        "acc_name"=> $Withdraw->bname,
                        "acc_no"=> $Withdraw->bank_id,
                        "order_amount"=> $Withdraw->price,
                        "order_no"=> $Withdraw->order_no,
                    );
            
                file_get_contents($api_url, false, stream_context_create(array(
                        'http' => array(
                            'method' => 'GET',
                            'header' => 'Content-type: application/json',
                            'content' => json_encode($params)
                        )
                    )));
            }
            else{//发起代付失败

                PlayCommon::play_error($Withdraw->order_no,-1,'bank is not found');

            }

         }
         else if($Withdraw->status==8){  
            //allpay请求代付
            $api_url = env('APP_URL').'/api/playfastpay?bank_server=allpay&action=payment';
            $bankname = strtoupper(str_replace(' ', '',$Withdraw->bank_name));
            $bankcode =  BankCode::get_bankcode('allpay',$bankname);

            if($bankcode!=404){
                $params = array(
                    "acc_code"=> $bankcode,
                    "acc_name"=> $Withdraw->bname,
                    "acc_no"=> $Withdraw->bank_id,
                    "order_amount"=> $Withdraw->price,
                    "order_no"=> $Withdraw->order_no,
                );
        
            file_get_contents($api_url, false, stream_context_create(array(
                    'http' => array(
                        'method' => 'GET',
                        'header' => 'Content-type: application/json',
                        'content' => json_encode($params)
                    )
                )));
           }
         
         }
         else{  //审核驳回


            $yuan_price = $user->price + $Withdraw->price;

            $user_updata = User::where('uid',$Withdraw->uid)->update(['price'=>$yuan_price]);

         }

    }


    
    //获取提现数量
    protected function getcount()
    {
         $Withdraw = Withdraw::where('status',0)->get();
         $count = count($Withdraw);
         
         return Tools::Returnajax($count,'获取成功',200);

    }

}
