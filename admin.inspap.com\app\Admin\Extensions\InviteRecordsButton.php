<?php

namespace App\Admin\Extensions;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Lang;

class InviteRecordsButton extends AbstractTool
{
    protected function html()
    {
        $url = admin_url('staff-code/invite-records/' . $this->getKey());
        
        return <<<HTML
<div class="btn-group" style="margin-right: 5px">
    <a href="{$url}" class="btn btn-sm btn-primary">
        <i class="fa fa-eye"></i> &nbsp;{$this->trans('查看邀请记录')}
    </a>
</div>
HTML;
    }

    protected function trans($key)
    {
        return Lang::has($key) ? trans($key) : $key;
    }
} 