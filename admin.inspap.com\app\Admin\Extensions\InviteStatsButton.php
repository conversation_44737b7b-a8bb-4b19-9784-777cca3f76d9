<?php

namespace App\Admin\Extensions;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\DB;

class InviteStatsButton extends AbstractTool
{
    protected function script()
    {
        return <<<JS
$('.invite-stats-btn').on('click', function () {
    var url = $(this).data('url');
    layer.open({
        type: 2,
        title: '邀请统计',
        area: ['80%', '80%'],
        content: url
    });
});
JS;
    }

    public function render()
    {
        // 获取总体统计数据
        $totalInvites = DB::table('invite_records')->count();
        $todayInvites = DB::table('invite_records')
            ->whereDate('created_at', today())
            ->count();
        $activeUsers = DB::table('users')
            ->where('last_login', '>', now()->subDays(30))
            ->count();

        $url = admin_url('auth/invite-stats');

        return <<<HTML
<div class="btn-group" style="margin-right: 5px">
    <a href="javascript:void(0);" data-url="{$url}" class="btn btn-primary invite-stats-btn">
        <i class="fa fa-bar-chart"></i>
        <span>邀请统计</span>
    </a>
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <span class="caret"></span>
            <span class="sr-only">Toggle Dropdown</span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="#">总邀请：{$totalInvites}</a></li>
            <li><a href="#">今日邀请：{$todayInvites}</a></li>
            <li><a href="#">活跃用户：{$activeUsers}</a></li>
        </ul>
    </div>
 