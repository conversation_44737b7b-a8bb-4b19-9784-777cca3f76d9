<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use Dcat\Admin\Admin;
use App\Http\Controllers\Lang;
class IndexDateForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        // dump($input);

        // return $this->response()->error('Your error message.');

       $data = [
        'start_at' => $input['start_at'],
        'end_at'   => $input['end_at'],
       
       ];
          
       Admin::script(
            <<<JS

            Dcat.ready(function () {

        
                setCountdata(1111);
            });

            JS
        );

        return $this
				->response()
				->success(Lang::translate('成功'))
				->refresh();

    }

    protected function savedScript()
    {

      return  <<<JS

      Dcat.ready(function () {

          console.log(data);
          setCountdata();
      });

      JS;

    }
    /**
     * Build a form here.
     */
    public function form()
    {

        $this->column(6, function () {
            $this->dateRange('start_at','end_at',Lang::translate('时间范围'));
       
        });

       
      
      
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {

       
    }
}
