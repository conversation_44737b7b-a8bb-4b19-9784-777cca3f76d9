<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Dcat\Admin\Form as Fm;
use App\Models\UsersDm;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
class MemberDmForms extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    // 处理表单提交请求
    public function handle(array $input)
    {
        // dump($input);

        // return $this->response()->error('Your error message.');
        $where = [
            ['uid',$input['form_uid']]
        ];

        $stauts = $input['status']; 
        $price  = $input['zprice'];
        $res = UsersDm::where($where)->first();

        if($res){
        if($stauts==1){ //增加

            $countPrice = $res->outdm + $price;

        }
        else{
            $countPrice = $res->outdm - $price;
        }
       
        $es = UsersDm::where($where)->update(['outdm' =>$countPrice]);
        return $this->response()->success(Lang::translate('成功'))->refresh();
        }
       
        else{
            return $this->response()->error(Lang::translate('用户不存在'));
        }
       
    }



    /**
     * Build a form here.
     */
    public function form()
    {

        $this->text('price',Lang::translate('金额'))->disable();
        $this->text('minprice',Lang::translate('出款所需打码量'))->disable();
        $this->text('nowprice',Lang::translate('当前打码量'))->disable();
        $this->radio('status') ->options([
            1 => Lang::translate('人工增加'),
            2 => Lang::translate('人工扣除'),
        ])->default(1,true);//设置默认状态  $form->model()->status 获取字段值
        $this->html(Lang::translate('金额增加：增加用户可提现金额 / 扣除：扣除用户可提现金额'), $label = '');
        $this->text('zprice',Lang::translate('金额'))->required();
        $this->hidden('form_uid')->value('-');
        $this->confirm(Lang::translate('打码量确认'));
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        // $where = [
        //     ['uid','uid_111111']
        // ];

        // $rs = UsersDm::where($where)->first();
        // $us = $res = DB::table('users')->where($where)->first();


        // return [
        //     'price' =>     $us->price,
        //     'minprice' =>  $rs->outdm,
        //     'nowprice' =>  $rs->nowdm,
        // ];
    }
}
