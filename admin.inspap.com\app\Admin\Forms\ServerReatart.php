<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Models\ProductOrdering as order;
use App\Http\Controllers\Lang;
class ServerReatart extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {

        //重启开奖

        
        $order  = order::where('status',1)->get();

        if(count($order)>0){
            app('App\Http\Controllers\CrontabController')->index('restart'); 

        }


       
        //重设开奖预设
        app('App\Http\Controllers\GeneratorController')->run_gener();   
       


        return $this
				->response()
				->success(Lang::translate('成功'))
				->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {

        $this->disableResetButton();
        $ok = '<span style="background:#21b978;color:#fff" >服务正常</span>';
        $erro = '<span style="background:red;color:#fff" >服务异常</span>';
        $res = DB::table('product_tmp')->first();

        $now_time = time();

        if($now_time - $res->open_at < 0){
            $status = $ok;
        }
        else{
            $status = $erro;
        }

        $this->html(Lang::translate('运行状态:'.$status),$label = '');
       
        $this->confirm(Lang::translate('确认重启产品服务吗？'));

    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {

    }
}
