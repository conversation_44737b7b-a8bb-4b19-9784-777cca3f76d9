<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;

use App\Http\Controllers\Lang;
class Setting extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        // dump($input);

        // return $this->response()->error('Your error message.');

       $data =[

        'sitename' => $input['sitename'],
        'minPrice' => $input['minPrice'],
        'wtimes'   => $input['wtimes'],
        'banner'   => $input['banner'],
        'is_int'   => $input['is_int'],
        'extension'=> $input['extension'],
        'credit'=> $input['credit'],
        
       ];
       
       $res = DB::table('system')->where('id','=',1)->update($data);
       


        return $this
				->response()
				->success(Lang::translate('成功'))
				->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->text('sitename',Lang::translate('网站名'))->required();
      
        $this->text('minPrice',Lang::translate('最小提款'))->required();
        $this->text('wtimes',Lang::translate('提现次数'))->required();
        $this->switch('is_int',Lang::translate('是否整数'));
        $this->text('credit',Lang::translate('信用分'));
        //$this->image('banner')->saveFullUrl();
        $this->multipleImage('banner',Lang::translate('轮播图'))->saving(function ($paths) {
            // 可以转化为由 , 隔开的字符串格式
            // return implode(',', $paths);

            // 也可以转化为json
            return json_encode($paths);
        });
        $this->text('extension',Lang::translate('客服链接'));
      
        
       
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {

        $res = DB::table('system')->first();

        if($res){
            return [
                'sitename'  => $res->sitename,
                'minPrice' => $res->minPrice,
                'wtimes'  => $res->wtimes,
                'banner'  =>  json_decode($res->banner, true),
                'is_int'  => $res->is_int,
                'extension' => $res->extension,
                'credit' => $res->credit
            ];
        }
       
    }
}
