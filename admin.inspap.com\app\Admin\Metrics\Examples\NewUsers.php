<?php

namespace App\Admin\Metrics\Examples;

use Dcat\Admin\Widgets\Metrics\Line;
use Illuminate\Http\Request;

class NewUsers extends Line
{
    /**
     * 初始化卡片内容
     *
     * @return void
     */


     public function __construct($names){

        $this->some_name = $names;
        $this->init();
    }


    protected function init()
    {
      
       parent::init();
       $this->title($this->some_name);
        
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return mixed|void
     */
    public function handle(Request $request)
    {
        $namess = $this->withChart([]);


        if($namess=='充值金额'){
            $this->withContent(mt_rand(1000, 5000).'g');
        }

        else if($namess=='注册人数'){
            $this->withContent(mt_rand(1000, 5000).'z');
        }
        else if($namess=='提现金额'){
            $this->withContent(mt_rand(1000, 5000).'t');
        }
        else{
            $this->withContent($namess.'k');

        }
       

    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data)
    {
        // return $this->chart([
        //     'series' => [
        //         [
        //             'name' => $this->title,
        //             'data' => $data,
        //         ],
        //     ],
        // ]);


        return $this->some_name;
    }

    /**
     * 设置卡片内容.
     *
     * @param string $content
     *
     * @return $this
     */
    public function withContent($content)
    {
        return $this->content(
            <<<HTML
            <div class="d-flex justify-content-between align-items-center mt-1" style="margin-bottom: 2px">
                <h2 class="ml-1 font-lg-1">{$content}!!</h2>
                <span class="mb-0 mr-1 text-80">{$this->title}??</span>
            </div>
            HTML
        );
    }
}
