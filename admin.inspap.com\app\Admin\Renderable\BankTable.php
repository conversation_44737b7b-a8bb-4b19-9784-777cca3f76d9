<?php

namespace App\Admin\Renderable;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Grid\LazyRenderable;
use App\Admin\Repositories\UsersBankInfo;
use App\Admin\Forms\Bank;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\DB;
use usIlluminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Widgets\Card;
use App\Http\Controllers\Lang;
class BankTable extends LazyRenderable
{


    public function grid(): Grid
    {
        return Grid::make(new UsersBankInfo(), function (Grid $grid) {
     
            $grid->withBorder();
            $grid->model()->where('uid',$this->uid)->orderByDesc('created_at');
            $grid->column('id');
            // 编辑成功后刷新页面
            $grid->column('uid')->display(function ($res) {
                $where = [
                    ['uid',$this->uid]
                ];
                $res = DB::table('users')->where($where)->first();
                $name = ($res) ? $res->uname : Lang::translate('暂无');
                return $name;
            });
            $grid->column('bank_name')->editable(true);
            $grid->column('bank_id')->editable(true);
            $grid->column('created_at');


            $grid->filter(function (Grid\Filter $filter) {
            $filter->equal('id');
            
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableDelete();
               // $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
                $actions->disableEdit();  //禁用普通编辑 
               // $actions->QuickEdit();    // 启用快速编辑（弹窗）

            });

        });
    }


}
