<?php

namespace App\Admin\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Grid\LazyRenderable;
use App\Admin\Repositories\UsersBankInfo;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;

class BankTable extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(new UsersBankInfo(), function (Grid $grid) {

            $grid->withBorder();
            $grid->model()->where('uid',$this->uid)->orderByDesc('created_at');
            $grid->column('id');
            // 编辑成功后刷新页面
            $grid->column('uid','用户')->display(function ($res) {
                $where = [
                    ['uid',$this->uid]
                ];
                $res = DB::table('users')->where($where)->first();
                $name = ($res) ? $res->uname : '暂无';
                return $name;
            });
            $grid->column('bank_name','银行名称')->editable(true);
            $grid->column('bank_id','银行卡号')->editable(true);
            $grid->column('created_at','创建时间');


            $grid->filter(function (Grid\Filter $filter) {
            $filter->equal('id');
            
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
               // $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
            });

        });
    }

      /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new UsersBankInfo(), function (Form $form) {
         
            $form->hidden('bank_name');
            $form->hidden('bank_id');
           

        });
    }

}
