<?php



namespace App\Admin\Renderable;



use Dcat\Admin\Grid;

use Dcat\Admin\Grid\LazyRenderable;

use App\Admin\Repositories\UsersBillInfo;

use Dcat\Admin\Http\Controllers\AdminController;

use Illuminate\Support\Facades\DB;

use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
class BillTable extends LazyRenderable

{

    public function grid(): Grid

    {



       



        return Grid::make(new UsersBillInfo(), function (Grid $grid) {



           

            $grid->withBorder();

            $grid->model()->where('uid',$this->uid)->orderByDesc('created_at');

            $grid->column('id');

            $grid->column('action',Lang::translate('操作'))->display(function () {

                

                $actionType = Tools::actionType(); //操作方式



                return $actionType[$this->action];

                

            });

            $grid->column('price');

            $grid->column('amount',Lang::translate('变更前金额'));

            $grid->column('created_at');



            $grid->filter(function (Grid\Filter $filter) {

            $filter->equal('id');

            });



            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableDelete();

               // $actions->disableEdit();  //禁用普通编辑 

                $actions->disableView();

                $actions->disableEdit();  //禁用普通编辑 

               // $actions->QuickEdit();    // 启用快速编辑（弹窗）

            });



        });

    }



}

