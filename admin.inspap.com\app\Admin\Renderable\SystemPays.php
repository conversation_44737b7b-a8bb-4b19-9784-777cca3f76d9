<?php



namespace App\Admin\Renderable;

use Dcat\Admin\Grid;

use Dcat\Admin\Grid\LazyRenderable;

use App\Admin\Repositories\SystemPay;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Http\Controllers\AdminController;

use Illuminate\Support\Facades\DB;
use Dcat\Admin\Form;
use App\Http\Controllers\Tools;
use App\Http\Controllers\Lang;
class SystemPays extends LazyRenderable

{

    public function grid(): Grid

    {


        return Grid::make(new SystemPay(), function (Grid $grid) {

            $grid->disableCreateButton();
            $grid->withBorder();

            $grid->column('id');

        
            $grid->column('payname',Lang::translate('服务商'));

            $grid->column('month_name',Lang::translate('支付名'));

            $grid->column('month_code',Lang::translate('支付代码'));

            $grid->column('status',Lang::translate('状态'))->display(function () {

                    if($this->status==0){
                        return Lang::translate('关闭');

                    }
                    else{
                        return Lang::translate('打开');
                    }


            });

            $grid->filter(function (Grid\Filter $filter) {

                $filter->equal('id');

            });



            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableDelete();
               // $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
                $actions->disableEdit();  //禁用普通编辑 

                  // append一个操作
                //   $bill = Modal::make()
                //   ->xl()
                 
                //   ->title(Lang::translate('编辑'))
                //   ->body( Form::make(new SystemPay(), function (Form $form) {
                //         $form->display('payname');
                //         $form->display('month_name');
                //         $form->display('month_code');
                //         $form->switch('status');
        
                //  })) // Modal 组件支持直接传递 渲染类实例
                //   ->button('<i class="fa fa-file-text-o" style="margin-right: 10px;"></i>')->edit($actions->getKey());
                //   $actions->prepend($bill);
                $actions->append('<a href="/admin/setplay/'.$actions->getKey().'/edit">   
                <span class="fa fa-edit" style="color: #f39c12;"></span> <span style="color: #1e282c">编辑</span>
                </a>');

            });



        });

    }



}

