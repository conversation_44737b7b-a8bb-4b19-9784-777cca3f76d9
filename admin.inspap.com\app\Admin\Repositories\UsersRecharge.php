<?php

namespace App\Admin\Repositories;

use App\Models\UsersRecharge as Model;
use Dcat\Admin\Repositories\EloquentRepository;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Grid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Dcat\Admin\Admin;
class UsersRecharge extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function get(Grid\Model $model)
    {
        // 获取当前页数
        $currentPage = $model->getCurrentPage();
        // 获取每页显示行数
        $perPage = $model->getPerPage();

        // 获取筛选参数
        $admin   = Auth::guard('admin')->user(); 

        $roles = Admin::user()->roles; //获取权限分组

        $where = [];

        if($roles[0]->slug == 'staff'){   //如果是员工角色 只看自己所属的用户
            $where = [
                  ['user.staff_id',$admin->id]
            ];
           

        }

        if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团
            
            $where = [
                ['user.team_id',$admin->team_id]
          ];
        }



        // 获取筛选参数

        $uname = $model->filter()->input('uname');
        $wuname = [];
        if($uname!=''){
            $wuname =[
                ['ur.uname',$uname]
            ];
        }
      
      
        $count = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select('ur.*')
        ->where($where)
        ->where($wuname)
        ->where('user.user_type',1)
        ->orderBy('ur.created_at','desc')
        ->get();
        

        $page = $currentPage - 1;

        $data = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select('ur.*')
        ->where($where)
        ->where($wuname)
        ->where('user.user_type',1)
        ->orderBy('ur.created_at','desc')
        ->offset($page * $perPage)
        ->limit($perPage)
        ->get();
        
        //$data = json_decode((string)$response->getBody(), true);

        return $model->makePaginator(
            $data['total'] ?? count($count), // 传入总记录数
            $data['subjects'] ?? $data // 传入数据二维数组
        );


    }
}
