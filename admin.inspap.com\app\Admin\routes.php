<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware','switchLan'),
], function (Router $router) {

    //每项的列表页
    $router->resource('member/list', 'UserController');
    $router->resource('member/demolist', 'UsersDemoController');
    $router->resource('member/recharge', 'UsersRechargeController');
    $router->resource('member/withdraw', 'UsersWithdrawController');
    $router->resource('product/manage', 'ProductController');
    $router->resource('product/tmplist', 'ProductTmpController');
    $router->resource('product/order', 'ProductTmpController');
    $router->resource('product/ordering', 'ProductOrderingController');
    $router->resource('product/productopen', 'ProductOpenController');
    $router->resource('contont/letter', 'ContentLetterController');
    $router->resource('contont/bulletin', 'ContentBulletinController');
    $router->resource('contont/action', 'ContentActionController');
    $router->resource('auth/code', 'StaffCodeController');
    $router->resource('users/lv', 'UsersVpiController');
    $router->resource('setbank', 'SystemBankController');
    $router->resource('setplay', 'SystemPayController');
    $router->resource('order', 'UserOrderController');

    //自定义页 OR 工具页
    $router->get('/', 'HomeController@index');
    $router->get('/site', 'HomeController@welecome');
    $router->get('/server-restart', 'HomeController@server_restart');
    $router->get('/memberdm', 'UserController@memberdm');
    $router->get('member/amount/{action}', 'UserController@member_amount');
    $router->get('member/online', 'UserController@online');
    $router->get('CountCenter/{action}', 'HomeController@CountCenter');
    $router->any('dcat-api/render/{id}', 'UsersBankInfoController@form_bank');
    
    $router->get('/changeLocale/{locale}', 'HomeController@changeLocale');

    // 强制开小/单的路由
    $router->get('get-force-big-odd', 'ProductTmpController@getForceBigOdd')->name('admin.get-force-big-odd');
    $router->post('toggle-force-big-odd', 'ProductTmpController@toggleForceBigOdd')->name('admin.toggle-force-big-odd');

    //API
    $router->group(['prefix' => 'api'], function($router) {
        // 产品相关API
        $router->get('product/get-current-issue', 'ApiController@getCurrentIssue');
        
        // 其他API
        $router->get('usersdm', 'UsersDmController@dm');
        $router->get('usersamount', 'UsersAmountController@amount');
        $router->get('outonline', 'UserController@outline');
        $router->get('getcount', 'CountController@index');
        $router->get('getcountcenter/{action}', 'CountCenterController@index');
        $router->get('withdrabill/{action}', 'UsersWithdrawController@updata_user_bill');  //提现后更新账单和余额
        $router->get('withdra/getcount', 'UsersWithdrawController@getcount');  //获取提现数量
    });
});