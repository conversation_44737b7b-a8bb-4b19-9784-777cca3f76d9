<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Api;
use App\Models\User;
use App\Http\Controllers\Controller;

class online extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:online';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '运行计划任务检测用户在线';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $now = time();

        $user = User::all();

        for($i=0;$i<count($user);$i++){


            if($user[$i]->api_token!=null){

                 $longtime = strtotime($user[$i]->updated_at);

                 if((int)$now - (int)$longtime > 7200){   //登录到现在超过了2小时  
                      User::where('api_token',$user[$i]->api_token)->update(['api_token'=> null]);
                 }
            }


        }

    }


}
