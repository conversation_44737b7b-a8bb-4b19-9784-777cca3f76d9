<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Http\Controllers\PlayCommon;
use App\Http\Controllers\PlayFastpay;
use App\Http\Controllers\PlayKbpay;

use App\Http\Controllers\Lang;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UsersBankInfo as Bank;
use Illuminate\Support\Facades\Auth;
use App\Models\ProductOrdering as order;
use App\Models\ProductTmp;
use App\Models\Product;
use App\Models\UsersDm;
use App\Models\UsersBillInfo;
use App\Models\UsersWithdraw;
use App\Models\ContentBulletin;
use App\Models\ContentAction;
use App\Models\SystemPay;

use App\Models\UsersRecharge;
use App\Models\ContentLetter;
use App\Models\UsersVpi;
use App\Models\SystemBank;
use App\Models\UsersBillInfo as BillTable;
class ApiController extends Controller
{
    function  __construct(){
	  // $this->middleware('auth');\
	  date_default_timezone_set('Asia/Shanghai');  //必须设置时区，不然坑的要死！！
    }
	/**会员自己注册 */
	function user_register(Request $request){

		 $username = $request->input('uname');

		 $staff  = DB::table('staff_code')->where('code',$request->input('code'))->first();
		 $isSet  = User::where('uname',$username)->get();
		 $system = DB::table('system')->find(1);
		 if(count($isSet)>0){

			return Tools::Returnajax($isSet,Lang::translate('账号已存在',$request),2002);

		 }
         if(!$staff){

			return Tools::Returnajax(null,Lang::translate('邀请码不正确',$request),2001);

		 }

		 $team_id  = DB::table('admin_users')->where('id',$staff->uid)->first();
		 $uid = 'uid_'.time();
		 $data = [
			'uid'        => $uid, //会员UID
			'staff_code' => $request->input('code'),
			'uname'      => $username,
			'password'   => Hash::make($request->input('password')),
			'bname'      => $request->input('bname') ?? '',
			'phone'      => $request->input('phone'),
			'staff_id'   => $staff->uid,
			'team_id'    => $team_id->team_id,
			'credit'     => $system->credit,
			'ip'         => $request->ip()
		];

		$res = User::create($data);

		if($res){


			UsersDm::create(['uname'=> $username, 'uid'=>$uid]);

		}

		return Tools::Returnajax($res,Lang::translate('注册成功',$request),200);

	}

	/*登录*/
    // function user_login(Request $request){
    //     $userdata = [
	// 	      'uname'    => $request->input('uname'),
	// 		  'password' => $request->input('password'),
	// 	];

	//     $user  =User::where('uname',$userdata['uname'])->firstOrfail();
	// 	if($user){

	// 		$access =  Hash::check($userdata['password'],$user->password);
    //         if($user->status!=2):
	// 			if($access):

	// 			//更新登陆信息
	// 			$token = Tools::getrandstr(36);
	// 			$updata = [
	// 				'updated_at'      => date('Y-m-d H:i:s'),
	// 				'online_status'   =>  1,
	// 				'remember_token' => Hash::make($token)
	// 			];
	// 			$user->update($updata);
	// 			//注册登陆信息
	// 			//session(['laravel_uid' => $user->uid]);

	// 			//session(['laravel_power' => $user->power]);
    //             $json = [
	// 				'uid' => $user->uid,
	// 				'uname' => $user->uname,
	// 				'token' => $token,
	// 			];
	// 			//auth('api')->attempt()
	// 			return  response()->json(['data' => $json,'msg' => '登陆成功','status'=>200]);


	// 			//->withCookie(cookie('laravel_uid',$user->uid,480))
	// 			//->withCookie(cookie('laravel_uname',$user->uname,480));
	// 			//->withCookie(cookie('laravel_power',$user->power,480));

	// 			else:
	// 			return Tools::Returnajax($access,'密码错误',2005);
	// 			endif;
	// 	    else:
	// 	    	return Tools::Returnajax(null,'账户已禁用',2005);
	// 	    endif;
	// 	}
	// 	else{
	// 		return Tools::Returnajax(null,'登录ID不存在',2006);
	// 	}

	// }

	/*退出登陆 */

    // function user_logout(){

	//      $user = Auth::user();

	// 	 //$user->update(['remember_token' => null]);
	// 	 return  response()->json(['data' =>$user,'msg' => '退出成功','status'=>200]);
	// }

	/**修改密码 */

	public function change_password(Request $request){

		$where = [
			['uid',$request->input('uid')]
		];

		$updata = [
			'password'   => Hash::make($request->input('password')),
		];

		$re = Tools::change_sql('users',$where,$updata);
		return Tools::Returnajax(true,Lang::translate('修改成功'),200);
	}


    //修改个人信息
	public function change_user_info(Request $request){

		$where = [
			['uid',$request->input('uid')],

		];

		$user = User::where($where)->first();
		$updata = [
			'nationality'   => $request->input('nationality') ?? $user->nationality, //国籍
			'birthday'      => $request->input('birthday') ?? $user->birthday, //出生日
			'sex'           => $request->input('sex') ?? $user->sex, //性别
			'email'         => $request->input('email') ?? $user->email, //邮件
			'phone'         => $request->input('phone') ?? $user->phone, //电话
		];

		$re = User::where($where)->update($updata);
		if($re){
			return Tools::Returnajax(true,Lang::translate('修改成功',$request),200);
		}
		else{
			return Tools::Returnajax(false,Lang::translate('失败',$request),2001);
		}

	}

	/**获取自己基本信息 */
	public function get_userinfo(Request $request){


	   $token =   hash('sha256',$request->input('api_token'));
       $user = User::where('api_token',$token)->first();
	   return Tools::Returnajax($user,Lang::translate('成功'),200);
	   //	return  response()->json(['data' =>$request->user(),'msg' => Lang::translate('成功'),'status'=>200]);

	}

	//获取用户可提现金额

	// public function get_user_dm(Request $request){


	// 	$userdm = UsersDm::where('uid',$request->input('uid'))->first();
	// 	$money  = $userdm->nowdm - $userdm->outdm;
	// 	return Tools::Returnajax($money,Lang::translate('成功'),200);
	// }

    //获取账单
	public function get_bill(Request $request){

        $res = UsersBillInfo::where('uid',$request->input('uid'))->get();

		$actionType = Tools::actionType(); //操作方式

		for($i=0;$i<count($res);$i++){

			$res[$i]->type = $actionType[$res[$i]->action];

			$res[$i]->action_user = Lang::translate('system',$request);

		}
		return Tools::Returnajax($res,Lang::translate('成功'),200);
	}

	/**定单操作 */

    public function ordering(Request $request,$action){

		if($action=='add'){  //下单

			$json  = $request->input('data');

			$pw = [
				['pid',$json[0]['pid']]
			];
			$pject      = Product::where($pw)->first();  //$json[0] 的理由 是前端传递过来的很有可能是数组 但是 PID 和 旗号都是公用的
			$issue      = $json[0]['issue'];
			$pid        = $json[0]['pid'];
			$closedTime = $pject->closed_time;
            $nowtime    = time();

			$user       = User::where('uid',$request->input('uid'))->first();
            //检查期号是否过期 检查期号是否可以下单 检查用户是否被禁止下单该期号

			$issue_where = [
				['issue',$issue],
				['pid',$pid],
				['status',1]   //后台设置 是否限制投注 不限制=1  限制=2
			];

			$issue_obj = ProductTmp::where($issue_where)->first();


            if($user->status==3){
				return Tools::Returnajax(false,Lang::translate('您已被禁止下单请联系客服',$request),2001);

			}

			if(!$issue_obj){ //后台设置 是否限制投注 不限制=1  限制=2  r如果设置了限制那么 $issue_obj 将无法查询到结果
				return Tools::Returnajax(false,Lang::translate('该期禁止下注或已过期',$request),2002);
			}


			$open_at = $issue_obj->open_at;

			$data_cha = (int)$open_at - (int)$nowtime;

			if($data_cha < $closedTime){  //当前时间 减去 开奖时间 小于 关闭时间后 不可下注

				return Tools::Returnajax($data_cha,Lang::translate('该期已锁单',$request),2003);
			}

			$ban_users = $issue_obj->ban_user;

			$in_ban = strstr($ban_users,$user->uname); //用户是否包含在禁止名单

			if($in_ban!=''){
				return Tools::Returnajax(false,Lang::translate('您已被禁止下注本期请尝试其他期',$request),2004);
			}



			//匹配数据 参数说明 pair_data 1=小 2=大 3=单 4=双




			$user_ = Auth::user();


			//判断余额
			$order_price = 0;

			$now_price = $user_->price;

			for($i=0;$i<count($json);$i++){

				$moneys =  $json[$i]['quantity'] * 1;  //默认每注都是1块钱

				$order_price+=$moneys;

			}
			if($now_price < $order_price){

				return Tools::Returnajax(false,Lang::translate('积分余额不足',$request),2005);

			}


			//正式下单开始
			$oid = date('Ymd',time()).time(); //订单号
			$order_money = 0;  //下注总额
			$re = true;
			for($i=0;$i<count($json);$i++){

				$money =  $json[$i]['quantity'] * 1;  //默认每注都是1块钱

				$data = [
					'oid'           => $oid,
					'uname'         => $user_->uname,
					'uid'           => $user_->uid,
					'status'        => 1,
					'lottery_price' => 0,   //中奖金额
					'profit'        => 0,   //实际盈利
					'msg'           => $json[$i]['msg'],
					'pair_data'     => $json[$i]['pair_data'],
					'quantity'      => $json[$i]['quantity'],   //下注数量
					'quantity_price'=> $money,        //下注金额
					'issue'         => $json[$i]['issue'],
					'pname'         => $json[$i]['pname'],
					'pid'           => $json[$i]['pid'],
				];

				$order_money+=$money;

				$res = order::create($data);

				if(!$res){
					$re = false;
				}

			}

			if($re){

				//下单成功增加打码量
				$dml = UsersDm::where('uid',$request->input('uid'))->first();

				// 检查$dml是否为空，如果为空则创建一个新记录
				if (!$dml) {
					// 创建用户打码量记录
					$dml = new UsersDm();
					$dml->uid = $request->input('uid');
					$dml->uname = $user_->uname;
					$dml->nowdm = 0;
					$dml->save();
				}

				$updm = $dml->nowdm + (int)$order_money;
				UsersDm::where('uid',$request->input('uid'))->update(['nowdm'=>$updm]);

				//更新余额 与 账单

				//余额
				$yu_price = $now_price - (int)$order_money;
				$up_users = User::where('uid',$user_->uid)->update(['price' => $yu_price]);

				//账单

				$create_data = [
					'uid'   => $user_->uid,
					'uname' =>  $user_->uname,
					'action' => 4,  //4表示下单操作
					'price' => 0 - (int)$order_money,  //下单的金额
					'amount' => $now_price,  //下单前金额
					'direction' => 2, //出账
					'action_user'=>  $user_->uname,

				];
				$bill = BillTable::create($create_data);



				return Tools::Returnajax($res,Lang::translate('成功'),200);
			}
			else{
				return Tools::Returnajax($data,Lang::translate('下注失败'),2002);
			}

	    }
		else if($action=='get'){

			   $page = $request->input('limit') ?? 20;

               $res = order::where('uid',$request->input('uid'))
               ->orderBy('created_at', 'desc') // 按创建时间倒序排列
               ->paginate($page);

			   $productType = Tools::productType(); //操作方式


			   $lang = $request->input('language') ?? 'CN';
			   $language = [];

			   for($i=0;$i<count($res);$i++){

				   $res[$i]->pair_name = $productType[$res[$i]->pair_data];

				   $pname = Product::where('pid',$res[$i]->pid)->first();

				   // 检查 $pname 是否为 null，避免访问 null 对象的属性
				   if ($pname && isset($pname->language)) {
					   $language = $pname->language;
					   $res[$i]->pname = isset($language[$lang]) ? $language[$lang] : '';
				   } else {
					   $res[$i]->pname = ''; // 如果产品不存在，设置空名称
				   }

			   }



			   return Tools::Returnajax($res,Lang::translate('成功'),200);
		}
		else{
			return Tools::Returnajax(false,Lang::translate('操作参数Action不存在'),404);
		}

    }

	/*前端银行卡操作*/

	public function bank(Request $request,$action){

		$user_ = Auth::user();
		if($action=='add'){  //添加银行卡
            $status  = $request->input('status') ?? 1;
			$data = [
				'uname'         => $user_->uname,
				'uid'           => $request->input('uid'),
				'bank_name'     => $request->input('bank_name'),
				'bank_id'       => $request->input('bank_id'),
				'status'        => $status,
			];

			if($user_->bname==''){
				User::where('uid',$request->input('uid'))->update(['bname' => $request->input('bname')]);
			}

			$res = Bank::insert($data);
			if($res){
				return Tools::Returnajax($res,Lang::translate('成功',$request),200);
			}
			else{
				return Tools::Returnajax(false,Lang::translate('添加失败',$request),2002);
			}

		}
		else if($action=='get'){  //获取银行卡

				$where = [
					['uid',$request->input('uid')]
				];
				$res = Bank::where($where)->get();


			return Tools::Returnajax($res,Lang::translate('成功'),200);
		}
		else if($action=='delete'){ //删除银行卡

			$id = $request->input('bank_id');

			$where = [
				['bank_id',$id]
			];

			$res = Bank::where($where)->delete();

			if($res){
				return Tools::Returnajax($res,Lang::translate('成功'),200);
			}
			else{
				return Tools::Returnajax([],'没有找到卡号',404);
			}

		}
		else if($action=='default'){ //默认银行卡

			$id = $request->input('bank_id');

			$where = [
				['bank_id',$id]
			];

			$res = Bank::where($where)->update(['status' => 2]);

			return Tools::Returnajax($res,Lang::translate('成功'),200);
		}
		else{

			return Tools::Returnajax(false,Lang::translate('操作参数Action不存在'),404);
		}


	}

	//健康检查接口
	function health(){
		$Lang = new Lang();

		// 简单的健康检查，返回服务器状态
		$healthData = [
			'status' => 'ok',
			'timestamp' => time(),
			'server_time' => date('Y-m-d H:i:s'),
			'version' => '1.0.0'
		];

		return Tools::Returnajax($healthData, $Lang->translate('成功'), 200);
	}

	//获取网站配置信息
    function system_info(){

		$Lang = new Lang();

		$res = DB::table('system')->where('id','=',1)->get();

		return Tools::Returnajax($res,$Lang->translate('成功'),200);

	}

    //获取商品列表
    function product_list(Request $request){

		$where = [
			['deleted_at','=',null]
		];

		$res = DB::table('product')->where($where)->get();
		$lang = $request->input('language');
		$language = [];
		for($i=0;$i<count($res);$i++){

			$language = json_decode($res[$i]->language);
            if($lang!='' && $language && property_exists($language, $lang)){
				$res[$i]->pname = $language->$lang;
			}
			else if($lang!='' && $language && property_exists($language, 'CN')){
				// 如果指定语言不存在，回退到中文
				$res[$i]->pname = $language->CN;
			}

		}

		return Tools::Returnajax($res,Lang::translate('成功'),200);

	}

	 //获取开奖历史
	 function product_open(Request $request){

		$page = $request->input('limit') ?? 15;
		$where = [];

		if($request->filled('pid')){   //获取特定某个商品的 全部期号
			$where = [
				['pid', $request->input('pid')]
			];
		}

		$res = DB::table('product_open')
		->where($where)
		->orderBy('id','desc')
		->paginate($page);


		return Tools::Returnajax($res,Lang::translate('成功'),200);

	}

	 //获取期号
	 function product_issue(Request $request){

		$page = $request->input('limit') ?? 15;

		$where = [];

		if($request->filled('pid')){   //获取特定某个商品的 全部期号
			$where = [
				['pid', $request->input('pid')]
			];
		}


		$res = DB::table('product_tmp')
		->where($where)
		->paginate($page);

		return Tools::Returnajax($res,Lang::translate('成功'),200);

	}

	//提现操作
	function withdraw(Request $request,$action){

		$user = User::where('uid',$request->input('uid'))->first();

		$system = DB::table('system')->first();

		if($action=='add'){

			$withdraw_money = $request->input('money');

			$ok = true;
			$mgs = Lang::translate('失败',$request);

			//判断系统设置的最小金额
			if($withdraw_money < $system->minPrice){

				$ok = false;
                $mgs = Lang::translate('小于最小提现金额',$request);

			}

			//判断提现金额是否大于余额
			if($withdraw_money - $user->price > 0){
				$ok = false;
				$msg = Lang::translate('提现金额大于余额',$request);
			}

			//判断系统设置的是否整数
			if($system->is_int==1){

				if(is_int($withdraw_money)){

				}
				else{
					$ok = false;
                    $mgs = Lang::translate('提现金额应为整数',$request);
				}

			}
			//判断系统设置的提现次数


			$users = DB::table('users_withdraw')
			    ->where('uid',$request->input('uid'))
                ->whereDate('created_at',date('Y-m-d H:i:s',time()))
                ->get();

			if(count($users) > $system->wtimes){

				$ok = false;
                $mgs = Lang::translate('今日提现次数已用完',$request);
			}

			//判断用户打码量

			$userdm = UsersDm::where('uid',$request->input('uid'))->first();

			// 检查用户打码量记录是否存在，如果不存在则创建
			if (!$userdm) {
				$userdm = new UsersDm();
				$userdm->uid = $request->input('uid');
				$userdm->nowdm = 0;
				$userdm->outdm = 0;
				$userdm->save();
			}

			if($userdm->nowdm - $userdm->outdm < 0 ){

				$ok = false;
                $mgs =Lang::translate('打码量不足',$request);

			}

			//判断用户信用分

			if($user->credit < 100){

				$ok = false;
                $mgs =Lang::translate('Không đủ tín dụng',$request);


			}

			//判断提现金额是否超过打码量

			// if($userdm->nowdm - $withdraw_money < 0 ){

			// 	$ok = false;
            //     $mgs =Lang::translate('提现金额大于打码量',$request);

			// }



			// if( $withdraw_money > $userdm->nowdm - $userdm->outdm  ){

			// 	$ok = false;
            //     $mgs = Lang::translate('提现金额大于当前最大可提现金额').':'.$userdm->nowdm - $userdm->outdm;

			// }

            if($ok){
				$data = [
					'order_no'    =>  date('Ymd',time()).time().rand(10, 100),
					'uid'         => $user->uid, //会员UID
					'system_msg'  => $user->msg,
					'price'       => $withdraw_money,//提现金额
					'amount'      => $user->price, //原来账户剩余金额
					'bname'       => $user->bname,  //持卡人
					'bank_name'   => $request->input('bank_name'), //银行卡
					'bank_id'     => $request->input('bank_id'),   //银行卡号
					'action_user' => 'admin',
				];

				$res = UsersWithdraw::create($data);

				//更新用户余额


				$user_now_price = $user->price - $withdraw_money;

				$user_updata = User::where('uid',$user->uid)->update(['price'=>$user_now_price]);

				return Tools::Returnajax(true,Lang::translate('已提交提现申请'),200);
		    }
			else{


				return Tools::Returnajax(false,$mgs,2002);
			}
		}


		if($action=='get'){

			$res = UsersWithdraw::where('uid',$request->input('uid'))->get();

			return Tools::Returnajax($res,Lang::translate('成功'),200);

		}



	}

	//公告，活动


	//活动
	function centerinfo_action(Request $request){

		    $language = $request->input('language');

            $where = [

				//['staff_id',$request->input('staff_id')],
				['class',$language]
			];

			$res = ContentAction::where($where)->get();

			return Tools::Returnajax($res,'成功',200);

	}


	//公告
	function centerinfo_bulletin(Request $request){
		    $language = $request->input('language');
			$where = [

				['staff_id',$request->input('staff_id')],
				['class',$language]
			];
			$res = ContentBulletin::where($where)->get();

			return Tools::Returnajax($res,'成功',200);

	}


	//公告弹窗已查看
	function centerinfo_bulletin_viewed(Request $request){

		$where = [
			['id',$request->input('id')],
		];

		$uid = $request->input('uid');


		$res = ContentBulletin::where($where)->first();

		$array = [];

		if($res->viewed!=null){

			$json = json_decode($res->viewed);

			 array_push($json,$uid);

			 $updata = json_encode($json);

		 }
		 else{
			 $updata = json_encode([$uid]);

		 }

		ContentBulletin::where($where)->update(['viewed' => $updata ]);

		return Tools::Returnajax(true,'已查看',200);

    }

    //获取站内信
	function letter(Request $request){



		$language = $request->input('language');
		$nowuser  = $request->input('uname');

		$uid = User::where('uname',$nowuser)->first();

		//获取admin,客服发送的没有所属的消息

		$amsg = [
			['team_id',null],

		];

		$admin_msg = ContentLetter::where($amsg)->get();



		$where = [
			['staff_id',$request->input('staff_id')],
			['class',$language],
			['target',2]
		];

		$res = ContentLetter::where($where)
		->orwhere('target_user','LIKE','%'.$nowuser.'%')
		->get();

		$list = [];
		for($i=0;$i<count($res);$i++){

			if($res[$i]->viewed!=null){
				$al = json_decode($res[$i]->viewed);
				if (!in_array($uid->uid,$al)) {
					array_push($list,$res[$i]);
				}
		    }
			else{
				array_push($list,$res[$i]);

			}

		}

		for($k=0;$k<count($admin_msg);$k++){

			if($admin_msg[$k]->viewed!=null){
				$al = json_decode($admin_msg[$k]->viewed);
				if (!in_array($uid->uid,$al)) {
					array_push($list,$admin_msg[$k]);
				}
		    }
			else{
				array_push($list,$admin_msg[$k]);

			}

		}

		return Tools::Returnajax($list,'成功',200);

	}

	 //站内信已查看
	 function letter_viewed(Request $request){

		$where = [
			['id',$request->input('id')],
		];

		$uid = $request->input('uid');


		$res = ContentLetter::where($where)->first();

		$array = [];

		if($res->viewed!=null){

		   $json = json_decode($res->viewed);

			array_push($json,$uid);

			$updata = json_encode($json);

		}
		else{
			$updata = json_encode([$uid]);

		}
		ContentLetter::where($where)->update(['viewed' =>  $updata]);

		return Tools::Returnajax(true,'已查看',200);


	}

	//获取用户等级
	function get_user_lv(Request $request){

		$res = UsersVpi::all();

		return Tools::Returnajax($res,'成功',200);

	}

	//获取银行列表
	function bank_list(Request $request){

		$res = SystemBank::all();

		return Tools::Returnajax($res,'成功',200);

	}

	//获取支付渠道
	function play_list(Request $request){

		$res = SystemPay::where('status',1)->get();

		return Tools::Returnajax($res,'成功',200);

	}


	//获取用户余额
	function get_yue(Request $request){


		//h
		$whb = [
			['uid',$request->input('uid')],
			['action',3],
		];
		$res = UsersDm::where('uid',$request->input('uid'))->first();
        /** 心逻辑*/
		// 如果用户不存在，创建一个空对象进行处理
		if (!$res) {
			$res = new \stdClass();
			$res->nowdm = 0;
			return Tools::Returnajax($res,'success',200);
		}

		$bill = DB::table('users_bill_info')
       ->where($whb)
        ->select(DB::raw('sum(price) as bill_money'))
        ->first();

		$wti = [
			['uid',$request->input('uid')],
			['status',0],
		];

		$withdraw = DB::table('users_withdraw')
        ->where($wti)
        ->select(DB::raw('sum(price) as wit_money'))
        ->first();

		$bill_money = $bill && isset($bill->bill_money) ? $bill->bill_money : 0;
		$wit_money = $withdraw && isset($withdraw->wit_money) ? $withdraw->wit_money : 0;

		$yue = $res->nowdm - abs($bill_money) - $wit_money;

		if($yue<0){
			$yue = 0;
		};
		$res->nowdm = $yue;
       /*end */

		return Tools::Returnajax($res,'success',200);

	}


	//支付相关
	function PlayFastpay(Request $request){

		$bank_server = $request->input('bank_server');

		$action = $request->input('action');

		if($bank_server=='fastpay'){
			$play = new PlayFastpay();

		}
		if($bank_server=='nicepay'){
			$play = new PlayNicepay();

		}
		if($bank_server=='kbio'){
			$play = new PlayKbpay();
		}

		if($bank_server=='allpay'){
			$play = new PlayAllpay();
		}


		if($action=='pay'){//收款
			return $play->pay($request);
		}

		if($action=='query'){//查询
			//只能查询fastpay
			return $play->query($request,'trade.check');
		}

		if($action=='payment'){//代付

			return $play->payment($request);
		}

	}

    //代收回调

	public function PlayFastpayBack(Request $request,$server){


		if($server=='nicepay'){
			$play = new PlayNicepay();
			$res = $play->query($request);
			if(!$res){
			    echo "error"; exit;
			}

			$order_no = $request->input('order');

			$price = $request->input('amount'); //实际处理金额


			PlayCommon::play_($order_no,$price,Tools::onlineType('nicepay'),$request);  //参数：订单,金额,充值方式,回掉的参数

			return 'success';

	    }

		if($server=='kbio'){

			//UsersRecharge::where('order_no','202305231684854468')->update(['msg'=>'chenggledea']);


			$order_no = $request->input('order_no');
			$price = $request->input('trade_amount'); //实际处理金额
			if($request->input('trade_status') == 1){

				$play = new PlayKbpay();
				$string = $play->query($order_no);

				$json = json_decode($string, true);
				PlayCommon::play_($order_no,$price,6,$request);


				return 'success';

			}
			else{
				UsersRecharge::where('order_no',$order_no)->update(['msg'=>'waiting']);

				echo 'error';
			}

		}
		if($server=='allpay'){

			$play = new PlayAllpay();
			$res = $play->query($request);
			if(!$res){
			    echo "error"; exit;
			}

			$order_no = $request->input('mchOrderNo');

			$price = $request->input('amount'); //实际处理金额


			PlayCommon::play_($order_no,$price,Tools::onlineType('allpay'),$request);  //参数：订单,金额,充值方式,回掉的参数

			return 'success';

		}

	}

	//代付回掉

	public function playmentback(Request $request,$server){

		if($server=='allpay'){
			$play = new PlayAllpay();
			$string = $play->daifu_query($request);
			$order_no = $request->input('merTransferId');
			$amount = $request->input('transferAmount');
			$res = UsersWithdraw::where('order_no',$order_no)->first();

			if($string){
					//正式逻辑
					PlayCommon::playment_($res,$order_no,$amount,'代付成功');

					return 'success';


			}
			else{

					PlayCommon::playment_fild($res,$order_no,'error');
					return 'error';
			}

		}

		if($server=='nicepay'){

			    $play = new PlayNicepay();
				$string = $play->query($request);
			    $order_no = $request->input('order');
				$amount = $request->input('amount');
				$res = UsersWithdraw::where('order_no',$order_no)->first();

				if($string){
						//正式逻辑
						PlayCommon::playment_($res,$order_no,$amount,'代付成功');

						return 'success';


				}
				else{

						PlayCommon::playment_fild($res,$order_no,'error');
                        return 'error';
				}

	    }

		if($server=='kbio'){

			$play = new PlayKbpay();
			$order_no = $request->input('order_no');
			$res = UsersWithdraw::where('order_no',$order_no)->first();
			$trade_no = $request->input('trade_no');

            if($play->payment_query($request)){

				    $trade_amount = $request->input('trade_amount');

					//正式逻辑
					PlayCommon::playment_($res,$order_no,$trade_amount,$trade_no);

					return 'success';

			}
			else{
					PlayCommon::playment_fild($res,$order_no,$trade_no);
			}


		}
	}
}
