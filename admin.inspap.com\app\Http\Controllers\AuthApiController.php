<?php

// email 设置可为空
// request 和 response 都是 json 格式
// api_token 设置可插入数据库    

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Http\Controllers\Lang;
use App\Http\Controllers\Tools;
class AuthApiController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except('login', 'register');
    }

    protected function username()
    {
        return 'uname';
    }

    public function register(Request $request)
    {
        $this->validator($request->all())->validate();

        $api_token = Str::random(80);
        $data = array_merge($request->all(), compact('api_token'));
        $this->create($data);

        return compact('api_token');
    }

    protected function validator(array $data)
    {
        return Validator::make($data, [
            'uname' => ['required', 'string', 'max:255', 'unique:users',],
//            'email' => ['required', 'string', 'email', 'max:255',],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);
    }

    protected function create(array $data)
    {
        return User::forceCreate([
            'uname' => $data['uname'],
//            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'api_token' => hash('sha256', $data['api_token']),
        ]);
    }

    public function logout()
    {
        auth()->user()->update(['api_token' => null]);

        return ['message' => '退出登录成功'];
    }

    public function login(Request $request)
    {
        $user = User::where($this->username(), request($this->username()))->first();
        // $user = User::where($this->username(), request($this->username()))
        //     ->firstOrFail();
        
        if($user){
            if($user->status==2){
                    return Tools::Returnajax(null,Lang::translate('禁止登陆'), 2005);
            }
        }
        
        if (!$user) {
            return response()->json(['data'=>false,'msg' => Lang::translate('用户不存在'),'status'=>2002]);
        }
        
        
        if (!password_verify(request('password'), $user->password)) {
          return response()->json(['data'=>false,'msg' => Lang::translate('密码错误'),'status'=>2002]);
        }

     
        
        $api_token = Str::random(80);


        $user->update(
            ['api_token'      => hash('sha256', $api_token)],
            ['updated_at'     => date('Y-m-d H:i:s',time())],
            ['ip'             => $request->ip()],
        );
  

        return  response()->json(['data' => $api_token,'msg' => '登陆成功','status'=>200]);
     
    }

    public function refresh()
    {
        try {
            $user = auth()->user();
            if (!$user) {
                return response()->json([
                    'status' => 401,
                    'msg' => '用户未认证',
                    'data' => null
                ], 401);
            }

            $api_token = Str::random(80);
            $user->update(['api_token' => hash('sha256', $api_token)]);

            return response()->json([
                'status' => 200,
                'msg' => '会话刷新成功',
                'data' => [
                    'token' => $api_token
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'msg' => '会话刷新失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

 
}
