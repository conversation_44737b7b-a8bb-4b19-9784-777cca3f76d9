<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;

class BankCode extends Controller
{


    public function az(){


        return $this->get_bankcode('kbio','BANKOCBC–INDONESIA');
    }


    static function get_bankcode($servcr,$bankname){


        if($servcr=='kbio'){

            $list = [
                  
                'DANA'=> 	10879,
                'GOPAY'=> 10880,
                'OVO'=>   10881,
                'BANKCOMMONWEALTH'=>	10908,
                'BANKCTBC(CHINATRUST)INDONESIA'=>	10909,
                'BANKOCBC–INDONESIA'=>	10910,
                'BANKMAYBANKINDOCORP'=>	10911,
                'BANKMERINCORP'=>	10912,
                'BANKAGRIS'=>	10913,
                'LINKAJA'=>	10914,
                'INDOSATDOM<PERSON><PERSON>KU'=>	10915,
                'BPRKS'=>	10916,
                '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'=>	10917,
                'BANKVICTORIAINTERNATIONAL'=>	10918,
                'BANKMANDIRITASPENPOS'=>	10919,
                'BANKFAMAINTERNASIONAL'=>	10920,
                'CENTRATAMANASIONALBANK'=>	10921,
                'BANKINDEXSELINDO'=>	10922,
                'BANKMAYORAINDONESIA'=>	10923,
                'BANKMULTIARTASENTOSA'=>	10924,
                'BANKPURBADANARTA'=>	10925,
                'BANKARTOSIND'=>	10926,
                'BANKBCASYARIAH'=>	10941,
                'BANKKESEJAHTERAANEKONOMI'=>	10942,
                'ANGLOMASINTERNASIONALBANK'=>	10943,
                'LIMANINTERNATIONALBANK'=>	10944,
                'BANKAKITA'=>	10945,
                'BANKDIPOINTERNATIONAL(BANKSAHABATSAMPOERNA)'=>	10946,
                'BANKPERSYARIKATANINDONESIA'=>	10947,
                'PRIMAMASTERBANK'=>	10948,
                'BANKHARFA'=>	10949,
                'BANKINAPERDANA'=>	10950,
                'BANKSYARIAHMEGA'=>	10951,
                'BANKALFINDO(BANKNATIONALNOBU)'=>	10952,
                'BANKROYALINDONESIA'=>	10953,
                'ANKINDOMONEX(BANKSBIINDONESIA)'=>	10954,
                'BANKBRIAGRO'=>	10955,
                'BANKYUDHABHAKTI'=>	10956,
                'BANKMNC/BANKBUMIPUTERA'=>	10957,
                'BANKBINTANGMANUNGGAL'=>	10958,
                'BANKJASAJAKARTA'=>	10959,
                'BANKSRIPARTHA'=>	10960,
                'BANKBISNISINTERNASIONAL'=>	10961,
                'BANKSYARIAHMANDIRI(BSI)'=>	10962,
                'BANKBUKOPIN'=>	10963,
                'BANKBNISYARIAH'=>	10964,
                'BANKMEGA'=>	10965,
                'BANKBJBSYARIAH'=>	10966,
                'BANKSWAGUNA'=>	10967,
                'BANKTABUNGANPENSIUNANNASIONAL(BTPN)'=>	10968,
                'JENIUS'=>	10969,
                'BANKHIMPUNANSAUDARA1906'=>	10970,
                'BANKTABUNGANNEGARA(BTN)'=>	10971,
                'BANKQNBKESAWAN(BANKQNBINDONESIA)'=>	10972,
                'BANKHARMONIINTERNATIONAL'=>	10973,
                'BANK(BANKICBCINDONESIA)'=>	10974,
                'BANKWINDUKENTJANA'=>	10975,
                'BANKGANESHA'=>	10976,
                'BANKHAGAKITA'=>	10977,
                'BANKMASPIONINDONESIA'=>	10978,
                'BANKSINARMAS'=>	10979,
                'BANKMETROEXPRESS(BANKSHINHANINDONESIA)'=>	10980,
                'BANKMESTIKADHARMA'=>	10981,
                'BANKMUAMALAT'=>	10982,
                'BANKOFINDIAINDONESIA'=>	10983,
                'BANKNUSANTARAPARAHYANGAN'=>	10984,
                'BANKSULTRA'=>	10985,
                'BANKSULAWESITENGAH'=>	10986,
                'BANKBENGKULU'=>	10987,
                'BANKPAPUA'=>	10988,
                'BANKMALUKUMALUT'=>	10989,
                'BANKNTT'=>	10990,
                'BALI'=>	10991,
                'BANKNTB,NTBSYARIAH'=>	10992,
                'BANKSULUTGORONTALO'=>	10993,
                'BANKSULSELDANBARAT'=>	10994,
                'BANKKALTENG'=>	10995,
                'BANKKALIMANTANTIMURDANUTARA'=>	10996,
                'BANKKALIMANTANBARAT'=>	10997,
                'BANKKALSEL'=>	10998,
                'BANKLAMPUNG'=>	10999,
                'BANKSUMSELBABEL'=>	11000,
                'BANKRIAU'=>	11001,
                'BANKNAGARI'=>	11002,
                'BANKSUMUT'=>	11005,
                'BPDACEHSYARIAH'=>	11009,
                'BPDJAMBI'=>	11012,
                'BANKJATIM'=>	11015,
                'BANKJATENG'=>	11018,
                'BPDDIY'=>	11021,
                'BANKDKI'=>	11024,
                'BANKJABARDANBANTEN(BJB)'=>	11026,
                'BANKMAYAPADA'=>	11029,
                'BANKJTRUST'=>	11032,
                'BANKIFI'=>	11035,
                'BANKHAGA'=>	11037,
                'BANKANTARDAERAH'=>	11043,
                'BANKEKONOMI'=>	11046,
                'BANKBUMIARTA'=>	11049,
                'BANKOFCHINA'=>	11052,
                'BANKWOORIINDONESIA'=>	11055,
                'DEUTSCHEBANKAG'=>	11058,
                'BANKANZINDONESIA'=>	11061,
                'KOREAEXCHANGEBANKDANAMON'=>	11065,
                'BANKBNPPARIBASINDONESIA'=>	11067,
                'BANKCAPITALINDONESIA'=>	11071,
                'BANKKEPPELTATLEEBUANA'=>	11074,
                'BANKABNAMRO'=>	11077,
                'STANDARDCHARTEREDBANK'=>	11081,
                'BANKMIZUHOINDONESIA'=>	11087,
                'BANKRESONAPERDANIA'=>	11088,
                'BANKDBSINDONESIA'=>	11089,
                'BANKSUMITOMOMITSUIINDONESIA'=>	11090,
                'THEBANKOFTOKYOMITSUBISHIUFJLTD'=>	11091,
                'THEHONGKONG&SHANGHAIB.C.(BANKHSBC)'=>	11092,
                'THEBANGKOKBANKCOMP.LTD'=>	11093,
                'CREDITAGRICOLEINDOSUEZ'=>	11094,
                'BANKARTHAGRAHAINTERNASIONAL'=>	11095,
                'INGINDONESIABANK'=>	11096,
                'BANKOFAMERICA,N.A'=>	11097,
                'JP.MORGANCHASEBANK,N.A'=>	11098,
                'CITIBANK'=>	11099,
                'AMERICANEXPRESSBANKLTD'=>	11102,
                'BANKOCBCNISP'=>	11103,
                'BANKLIPPO'=>	11104,
                'BANKCIMBNIAGA'=>	11105,
                'BANKARTANIAGAKENCANA'=>	11106,
                'BANKPANIN'=>	11107,
                'BANKBIIMAYBANK'=>	11108,
                'BANKBCA'=>	11109,
                'PERMATABANK'=>	11110,
                'BANKDANAMON'=>	11111,
                'BANKBNI'=>	11112,
                'BANKMANDIRI'=>	11113,
                'BANKEKSPORINDONESIA'=>	11114,
                'BANKBRI'=>	11115,
                'BANKUOBINDONESIA'=>	11116,
                'BNC'=>	11117,

           ];

        }

        if($servcr=='fastpay'){

            $list = [
                    'ABBANK' => '12000f001',
                    'AGRIBANK' => '12000f002',	
                    'BIDVBANK'=> '12000f003',	
                    'BVB' =>'12000f004'	,
                    'CBBANK'=> '12000f005',	
                    'CIMB' =>'12000f006'	,
                    'DBS'=> '12000f007',	
                    'DONGABANK' =>'12000f008',
                    'EXIMBANK'=> '12000f009',	
                    'GPBANK'=> '12000f010',	
                    'HDBANK'=> '12000f011',	
                    'HSBC'=> '12000f012',	
                    'IVB'=> '12000f013',	
                    'KIENLONGBANK' =>'12000f014'	,
                    'LIENVIETPOSTBANK（LPB）' => '12000f015',	
                    'MARITIMEBANK（MSB）'=> '12000f016',	
                    'MBBANK'=> '12000f017',
                    'NAMABANK'=> '12000f018'	,
                    'NCB' =>'12000f019'	,
                    'NGANHANGACHAU' =>'12000f020'	,
                    'OCEANBANK'=> '12000f021'	,
                    'PBVN' =>'12000f022',	
                    'PGBANK' =>'12000f023',
                    'PHUONGDONGBANK'=> '12000f024'	,
                    'PVCOMBANK'=> '12000f025'	,
                    'SACOMBANK'=> '12000f026'	,
                    'SAIGONBANK'=> '12000f027',	
                    'SCB' =>'12000f028'	,
                    'SEABANK'=> '12000f029'	,
                    'SHBBANK' =>'12000f030',	
                    'TECHCOMBANK'=> '12000f031',	
                    'TIENPHONGBANK'=> '12000f032'	,
                    'VABBANK'=> '12000f033',	
                    'VIBBANK'=> '12000f034'	,
                    'VIETCAPITALBANK'=> '12000f035'	,
                    'VIETBANK'=> '12000f036'	,
                    'VIETCOMBANK' =>'12000f037',	
                    'VIETINBANK' =>'12000f038',
                    'VPBANK' =>'12000f039',	
                    'VRB'=> '12000f040'	,
                    'WOORI' => '12000f041',
            ];
        }
        if($servcr=='nicepay'){

            $list = [
                'BANKBCA' =>	'014',
                'BANKBNI' =>	'009',
                'BANKBRI'=>	'002',
                'BANKCIMBNIAGA'=>	'022',
                'BANKPANIN' => '019',
                'BANKROYALINDONESIA' => '501',
                'BANKMANDIRI' => '008',
                'PERMATABANK' => '013',
                'BANKSYARIAHMANDIRI(BSI)' => '451',
                'BANKJATIM' =>'114',
                'BANKUOBINDONESIA' =>'023',
                'MANDIRI' =>'008',
            ];
        }
        if($servcr=='allpay'){

            $list = [
                'BANKBCA' =>	'BCA',
                'BANKBNI' =>	'BNI',
                'BANKBRI'=>	'BRI',
                'BANKCIMBNIAGA'=>	'CIMB',
                'BANKPANIN' => 'PANIN',
                'BANKROYALINDONESIA' => 'ROYAL',
                'BANKMANDIRI' => 'MANDIRI',
                'PERMATABANK' => 'PERMATA',
            
                'BANKJATIM' =>'BANK_JATIM',
               
                'MANDIRI' =>'MANDIRI',
                 
            ];
        }


        $bankcode = 404;

        foreach($list as $key => $val){

            if($key == $bankname){
                $bankcode = $val;
            }
        }


        return $bankcode;
       

    }

}
