<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\ProductTmp;
use App\Models\ProductOpen;
use App\Models\ProductOrdering;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\UsersBillInfo as BillTable;

class CrontabController 
{
	function  __construct(){
       
        
	}

    public function index($type=''){


        $nowtime = date('Y-m-d H:i',time());
        $re   = ProductTmp::get();
        $tmp = [];   
        $n=0;

        if($type==''){
            for($i=0;$i<count($re);$i++){
                if($nowtime==date('Y-m-d H:i',$re[$i]->open_at)){
                  $tmp[$n] = $re[$i];
                  $n++;
                }
            }
          
        }
        else{
            
            for($i=0;$i<count($re);$i++){
                  $tmp[$n] = $re[$i];
                  $n++;
            }
        }
       
        
        if(count($tmp)>0 || $type=='restart'){  //到了开奖时间

            $data = [];
            $id = [];
            $whe = [];
            for($k=0;$k<count($tmp);$k++){ 

                $times = date('Y-m-d H:i:s',time());
                $id[$k] = $tmp[$k]->id;

                $whe = [
                    ['pid',$tmp[$k]->pid],
                    ['issue',$tmp[$k]->issue]
                ];

                $data[$k] = [
                    'pname' => $tmp[$k]->pname,
                    'pid'   => $tmp[$k]->pid,
                    'dx'    => $tmp[$k]->dx,
                    'sd'    => $tmp[$k]->sd,
                    'open_at' => $tmp[$k]->open_at,
                    'issue' => $tmp[$k]->issue, 
                    'source'=> 'auto',
                    'updated_at' => $times,
                    'created_at' => $times
                ];
            
                //用户订单匹配流程  status=2 匹配  status=-1 不匹配
                $update = [];
                $rs = ProductOrdering::where($whe)->get();

               
                if($rs){

                   
                    for($r=0;$r<count($rs);$r++){

                        $user_ =  User::where('uid',$rs[$r]->uid)->first();  
                        //$user_ =  Auth::user();
                        if($rs[$r]->pair_data==$tmp[$k]->dx){

                            $Win_lottery =  $rs[$r]->quantity_price *2;
                            $update = [
                                'status'=>2,
                                'lottery_price' => $Win_lottery,  //中奖金额
                                'profit'     => $rs[$r]->quantity_price,      //实际盈利
                                'updated_at' => $times,
                                'created_at' => $times
                            ];

                            $this->updata_user_bill($user_,$Win_lottery);
                        }
                        else if($rs[$r]->pair_data==$tmp[$k]->sd){

                            $Win_lottery =  $rs[$r]->quantity_price *2;
                            $update = [
                                'status'=>2,
                                'lottery_price' => $Win_lottery,  //中奖金额
                                'profit'     => $rs[$r]->quantity_price,      //实际盈利
                                'updated_at' => $times,
                                'created_at' => $times
                            ];

                         
                            $this->updata_user_bill($user_,$Win_lottery);

                        }
           
                        else{                             //大小没中
                            $update = [
                                'status'=>-1,
                                'lottery_price' => 0,  //中奖金额
                                'profit'  =>   0 - $rs[$r]->quantity_price,      //实际盈利
                                'updated_at' => $times,
                                'created_at' => $times
                            ];

                        }


                        $up_where = [
                            ['id',$rs[$r]->id]
                        ];

                        ProductOrdering::where($up_where)->update($update);  //更新即时注单 
                    }
                }
            }
            ProductOpen::insert($data);  //添加到开奖记录表
            ProductTmp::whereIn('id',$id)->delete(); //删除已开奖的预设

        }
        else{

        }

       

    }

     //更新账单
     protected function updata_user_bill($user,$add_price)
     {
         
             //更新用户金额

             $up_price = $user->price + $add_price;

             $user_updata = User::where('uid',$user->uid)->update(['price'=>$up_price]);
        
             //更新账单
             $create_data = [
                 'uid'   => $user->uid,
                 'uname' =>  $user->uname,
                 'action' => 4,  //4表示下单操作，中奖也属于下单的
                 'price' =>  $add_price,  //中将金额
                 'amount' => $user->price,  //中奖前金额
                 'direction' => 1, //出账
                 'action_user'=>  $user->uname,
 
             ];
             $bill = BillTable::create($create_data);
 

     }
   
}
