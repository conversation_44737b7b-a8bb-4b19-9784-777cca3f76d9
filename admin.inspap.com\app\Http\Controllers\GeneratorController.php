<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\Controller;
use App\Models\ProductTmp;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;
class GeneratorController extends Controller

{


    public function run_gener($obj=null){


        if($obj==null){
            $product = Product::get();  
            ProductTmp::truncate(); //清空预设表
        
            for($k=0;$k<count($product);$k++){

                $count = $k+1;
                $data[$k] = [
                    'pname' => $product[$k]->pname,
                    'pid'   => $product[$k]->pid,
                    'id'    => $product[$k]->id,
                    'qishu' => 60*60*24 / $product[$k]->period,  //总期数
                    'period'=> $product[$k]->period,    //多少秒1期
                ];
                $res = $this->get_rand($data[$k],$count);
        
                $ms  = ProductTmp::insert($res);
            
            }
        }
        else{

            $product = $obj;
            $count = $product->id;
            $data = [
                'pname' => $product->pname,
                'pid'   => $product->pid,
                'id'    => $product->id,
                'qishu' => 60*60*24 / $product->period,  //总期数
                'period'=> $product->period,    //多少秒1期
            ];
            $res = $this->get_rand($data,$count);
            $ms  = ProductTmp::insert($res);

        }
        

       return [
           'message' => $ms,
           'status'  => 200
       ];
    }


    public function get_rand($data,$count){

        $id    = $data['id'];
        $pname = $data['pname'];
        $pid   = $data['pid'];
        $qishu = $data['qishu'];
        $period= $data['period'];

        $array = [];  //生产结果合集
        $time = 0;
        $s = 0;

        for($i=0;$i<$qishu;$i++){
            $s += $period;
            $time =  time()+$s;
            array_push($array,$this->rand($id,$i,$time,$pid,$pname,$count));
            $time = 0;
        }

        return $array;

    }

    /**
     * 产生结果
     * n1,n2,n3 工具1，2，3
     * min = 小  max =大
     * 
     * $n 生成次数
     * @return Generator
     */


    protected function rand($id,$i_key,$time,$pid,$pname,$count)
    {
        $admin = Auth::guard('admin')->user(); 

        // 生成固定的数字组合，确保结果为小和单
        $n1 = [1,2,3,4,5,6];  
        
        // 固定生成小值（1-11之间的奇数）
        // 选择1、3、5、7、9、11中的随机数
        $possible_numbers = [1,3,5,7,9,11];
        $rand = $possible_numbers[array_rand($possible_numbers)];

        // 固定为小（1）
        $rand_dx = 1;

        // 固定为单（3）
        $rand_sd = 3;

        return [
            'pname'        => $pname,
            'pid'          => $pid,
            'issue'        => date('Ymd').$count.$id+$i_key,  //期号 时间戳 + 产品ID + 循环KEY
            'dx'           => $rand_dx,
            'sd'           => $rand_sd,
            'action_admin' => 'System',//$admin->username,
            'open_at'      => $time,//date('Y-m-d H:i:s',$time)
        ];
    }

}
