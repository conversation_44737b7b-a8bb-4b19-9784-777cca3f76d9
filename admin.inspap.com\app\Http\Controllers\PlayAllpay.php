<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Models\UsersRecharge;
use Illuminate\Support\Facades\DB;
use App\Models\UsersWithdraw;
use App\Http\Controllers\PlayCommon;
class PlayAllpay extends Controller
{

    // 商户号
    private $mer_no = '200288012';

    // 收款商户密钥
    private $mer_key = 'b340150d9ef349e98d28e0184f497e35';

    //代付商户密钥
    private $payout_keys ='JOJPHA4DRAJCEE6FDDXKBLRRXKZTMZJ3';
    
    //   // 商户号
    // private $mer_no = '200900002';

    // // 收款商户密钥
    // private $mer_key = '7fe73c012feb49caa29f8de1e4586be9';

    // //代付商户密钥
    // private $payout_keys ='MUSEGUIGELQGJV3SSLDEWJMENEYIYGIU';
    // 代收请求地址
    private $mer_pay_url = 'https://payment.allapay.com/pay/web';

    // 代付款请求地址
    private $fu_pay_url = 'https://payment.allapay.com/pay/transfer';


     //定义接口地址
     function http_url($action){

        if($action=='mer_return_url'){ //代收通知

            return env('APP_URL').'/api/playback/allpay';

        }

        else{ //代付通知
            return env('APP_URL').'/api/playmentback/allpay';
        }
        

    }

   // 对参数签名
   private function sign($params,$keys) {
    // params为json
    // 1.将json转为key=value&key=value的形式
    // 2.将key按照ASCII码排序
    // 3.空值不参与签名

    $str = '';
    foreach ($params as $key => $value) {
        if ($value != '') {
            $str .= $key . '=' . $value . '&';
        }
    }
    // 去掉最后一个&
    $str = substr($str, 0, -1);
    // 使用md5签名 转小写
    return md5( $str .'&key='.$keys);
}


//发起代收
public function pay($request) {
 
    $order = date('Ymd',time()).time();
    $order_date = date('Y-m-d H:m:s',time());

    $params = array(
        'mch_id'=>     $this->mer_no,
        'goods_name'=> 'online_pay',//商品名称
        'mch_order_no'=>$order, //订单号
        'notify_url'=>$this->http_url('mer_return_url'),//代收回掉地址
        'order_date'=>$order_date, //订单时间
        'pay_type'=> $request->input('paytypecode'), //支付类型
        'trade_amount'=> $request->input('amount'), //交易金额
        'version' => '1.0',
       
    );
    if($request->input('paytypecode')=='220'){
        $params['bank_code'] ='BCA'; //默认BCA

    };
    ksort($params);
    $this->order_create($request,$order);
    $params['sign'] = $this->sign($params,$this->mer_key);
    $params['sign_type'] ='MD5';
   
    $ch = curl_init();    
    curl_setopt($ch,CURLOPT_URL,$this->mer_pay_url); //支付请求地址
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));  
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $res=curl_exec($ch);

    curl_close($ch);

    $array = json_decode($res, true);
    $re = [];

    //重组数据结构
    if($array['respCode']=='SUCCESS'){
       $re = [
            'message' => 'success',
            'data'    => [
                   'url' => $array['payInfo']
             ]
          ];
    }
    else{
        $re = [
            'message' => 'Error:'.$array['tradeMsg'],
            'data'    => [
                   'url' => []
             ]
          ];
    }
    
    return $re;
}

   //发起代付
   public function payment($request) {

        $order_no = $request->input('order_no');
        $transtime = date('Y-m-d H:m:s',time());
        $sparams = array(
            'mch_id'=>$this->mer_no,
            'transfer_amount'=>$request->input('order_amount'),
            'apply_date' => $transtime,
            'receive_account'=>  $request->input('acc_no') , //银行卡号
            'receive_name'=>  $request->input('acc_name') , //持卡人
            'bank_code'=>  $request->input('acc_code') , //银行
            'mch_transferId'=> $order_no,
            'back_url'=> $this->http_url('playment_return_url')
        );
        
        
        ksort($sparams);
        $sparams['sign'] = $this->sign($sparams,$this->payout_keys);
        $sparams['sign_type'] ='MD5';

        $ch = curl_init();    
        curl_setopt($ch,CURLOPT_URL,$this->fu_pay_url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($sparams));  
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
        $response=curl_exec($ch);
    
    
        curl_close($ch);
        
       
        $array = json_decode($response, true);


        //代付请求失败
        if($array['respCode']=='FAIL'){

            $order = $order_no;
            //更新订单状态
            UsersWithdraw::where('order_no',$order)->update(['play_status' => -1,'sys_no'=>$array['errorMsg'] ]);

            PlayCommon::updata_user_pric($order);

        }

        $path = "./logs";
   
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log_allpay_daifu_cate.txt", "a");
		fwrite($myfile, $response);
		fclose($myfile); 
    
        return $response;
  }



   //这里其实是代收回调监测
   public function query($request) {

        $path = "./logs";
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log_allpay_daishou.txt", "a");
		fwrite($myfile, json_encode($request->all()));
		fclose($myfile); 
		$params = $request->all();
		$sign = $params['sign'];
		unset($params['sign']);
		$makesign = $this->sign($params,$this->mer_key);  //md5签名
		//if($sign == $makesign){
           
            if( $request->input('tradeResult') ==1){
                return true;
            }
            else{
                return false;
            }
		   

	//	}
		return false;

   }
   
//代付回调监测
   public function daifu_query($request) {

        $path = "./logs";
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log_allpay_daifu.txt", "a");
		fwrite($myfile, json_encode($request->all()));
		fclose($myfile); 
		$params = $request->all();
		$sign = $params['sign'];
		unset($params['sign']);
		$makesign = $this->sign($params,$this->payout_keys);  //md5签名
		//if($sign == $makesign){
           
            if( $request->input('tradeResult') ==1){
                return true;
            }
            else{
                return false;
            }
		   

	//	}
		return false;

   }

   //构建本地订单

   public function order_create($request,$order){
        $data = [
            'order_no' => $order,
            'type' => $request->input('paytypecode'),
            'price' =>  $request->input('amount'),
            'uname' =>  $request->input('uname'),
            'action_user' =>  $request->input('uname'),
            'uid' =>  $request->input('uid'),
            'amount' =>  $request->input('user_amount'),
            'action' => Tools::onlineType('allpay'),
            'status' => 0, //待生效
             'num'    => '待支付',
        ];
        
        UsersRecharge::create($data);

        $online = [
            'order_no' => $order,
            "payemail" =>  $request->input('payemail') ?? '<EMAIL>',
            "payname"  =>  $request->input('payname') ?? 'shana',
            "payphone" =>  $request->input('payphone') ?? '1324673',
            "paytypecode" => $request->input('paytypecode'),
        ];

        $id  =  DB::table('users_recharge_online')->insert($online);
   }

   

}
