<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Http\Controllers\Tools;
use App\Models\User;
use App\Models\UsersWithdraw;
use App\Models\UsersRecharge;
use App\Models\UsersDm;
use App\Models\UsersBillInfo as BillTable;



class PlayCommon extends Controller
{

  
    //公共代收回调
    static function play_($order_no,$price,$action,$request){

        $res = UsersRecharge::where('order_no',$order_no)->first();
					
        //记录充值次数
        $wh= [
            ['uid',$res->uid],
            ['status',1],
            ['action','!=','2'],  //排除彩金  彩金不统计为充值
        ];

        $list = UsersRecharge::where($wh)->get();

        $num = count($list) + 1;  

        $updata = [
            'status' => 1,  //将充值更改为生效
            'msg'=>$request->input('status'),
            'num'=> $num
        ];
        UsersRecharge::where('order_no',$order_no)->update($updata);


        /**处理打码量*/
    
        $get_dm = UsersDm::where('uid',$res->uid)->first();
        $dm_update = [
            'outdm' =>  $price + $get_dm->outdm
        ];
        $dm = UsersDm::where('uid',$res->uid)->update($dm_update);                    
        
        /**处理打码量 end*/


        $where = [
            ['uid',$res->uid]
        ];
        $uses = User::where($where)->first();

        /**处理账单 */
        $bill = new BillTable();
        $bill->uid    = $res->uid; 
        $bill->uname  = $res->uname; 
        $bill->action = $action ; 
        $bill->direction = 1; //1=加钱+  2=扣钱 -
        $bill->price =  $price ;
        $bill->amount = $uses->price;  //未变更前金额 直接获取 本次充值前 users表的price字段
        $bill->action_user = $res->uname; 
        $bill->msg  = '';
        $bill->save();
        /**处理账单 end*/

        /**更新用户主表金额 */


        $countPrice = $uses->price + $price;

        User::where($where)->update(['price' =>$countPrice]);
        
        return true;
    }


    //公共代付回调
    static function playment_($res,$order_no,$trade_amount,$sys_no){
        	
        $where = [
            ['uid',$res->uid]
        ];
        $uses = User::where($where)->first();

        /**处理账单 */
        $bill = new BillTable();
        $bill->uid    = $res->uid; 
        $bill->uname  = $res->uname; 
        $bill->action = 3 ; 
        $bill->direction = 2; //1=加钱+  2=扣钱 -
        $bill->price =  $trade_amount ; //提现金额(实际处理金额)
        $bill->amount = $res->amount;  //未变更前金额 直接获取 本次充值前 users表的price字段
        $bill->action_user = $res->uname; 
        $bill->msg  = '';
        $bill->save();
        /**处理账单 end*/

        /**更新用户主表金额 */


    
        // $countPrice =  $uses->price  - $trade_amount;

        // User::where($where)->update(['price' =>$countPrice]);

        $updatea = [
            'play_status' => 1,
            'sys_no'   =>$sys_no
        ];
        UsersWithdraw::where('order_no',$order_no)->update($updatea);
        return Tools::Returnajax($sys_no,'成功',200);
    }
    //公共代付失败回调
    static function playment_fild($res,$order_no,$trade_no){

            $orders =  UsersWithdraw::where('order_no',$order_no)->first();

            if($orders->play_status==0){
                //更新付款状态
                UsersWithdraw::where('order_no',$order_no)->update(['play_status' => -1,'sys_no' => $trade_no]);

                //退还用户积分 客户要求不退还用户积分
                // $user = User::where('uid',$res->uid)->first();

                // $user_updata = User::where('uid',$res->uid)->update(['price'=>$res->price + $user->price]);

                return Tools::Returnajax(false,'失败',2001);
            }
    }


    static function updata_user_pric($order){

        $res = UsersWithdraw::where('order_no',$order)->first();

        $user =  User::where('uid',$res->uid)->first();

        $yuan_price = $user->price + $res->price;
        
        $user_updata = User::where('uid',$res->uid)->update(['price'=>$yuan_price]);
    }

    
    //失败后更新订单状态，退还用户金额

    static function play_error($order_no,$status,$mgs){

        $ordeing = UsersWithdraw::where('order_no',$order_no)->first();

        UsersWithdraw::where('order_no',$order_no)->update(['play_status' => $status,'sys_no' => $mgs]);

        $user = User::where('uid',$ordeing->uid)->first();

        $yuan_price = $user->price + $ordeing->price;

        $user_updata = User::where('uid',$ordeing->uid)->update(['price'=>$yuan_price]);

        return true;
    }


}
