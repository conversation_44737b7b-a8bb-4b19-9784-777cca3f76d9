<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Models\UsersRecharge;
use Illuminate\Support\Facades\DB;
use App\Models\UsersWithdraw;
use App\Http\Controllers\PlayCommon;
class PlayFastpay extends Controller
{

   // 商户号
   private $mer_no = '1002963';

   // 商户密钥
   private $mer_key = 'c33c9c5ff728a951ac342296dfda7a34';

   // 请求地址
   private $mer_pay_url = 'http://www.fast-pay.cc/gateway.aspx';

   // 返回地址
   private $mer_return_url = 'https://admin.inspap.com/api/playback/fastpay';

   //代付 返回地址
   private $playment_return_url = 'https://admin.inspap.com/api/playmentback/fastpay';

//  // 返回地址  测试地址
//  private $mer_return_url = 'http://47.91.248.185/api/playback/fastpay';

//  //代付 返回地址
//  private $playment_return_url = 'http://47.91.248.185/api/playmentback/fastpay';

    //定义接口地址
    function http_url($action){

        if($action=='mer_return_url'){ //代收通知

            return env('APP_URL').'/api/playback/fastpay';

        }

        else{ //代付通知
            return env('APP_URL').'/api/playmentback/fastpay';
        }


    }


   // 对参数签名
   private function sign($params) {
    // params为json
    // 1.将json转为key=value&key=value的形式
    // 2.将key按照ASCII码排序
    // 3.空值不参与签名

    $str = '';
    foreach ($params as $key => $value) {
        if ($value != '') {
            $str .= $key . '=' . $value . '&';
        }
    }
    // 去掉最后一个&
    $str = substr($str, 0, -1);
    // 使用md5签名 转小写
    return strtolower(md5($str . $this->mer_key));
}


//发起代收
public function pay($request) {

    $order = date('Ymd',time()).time();
    $params = array(
        "currency" => "VND",
        "mer_no" => $this->mer_no,
        "method" => "trade.create",
        "order_amount" => $request->input('amount'),
        "order_no" => $order,
        "payemail" =>  $request->input('payemail') ?? '<EMAIL>',
        "payname"  =>  $request->input('payname') ?? 'shana',
        "payphone" =>  $request->input('payphone') ?? '1324673',
        "paytypecode" => $request->input('paytypecode'),
        "returnurl" => $this->http_url('mer_return_url'),
    );


    $this->order_create($request,$order);
    $params['sign'] = $this->sign($params);
    $res = file_get_contents($this->mer_pay_url, false, stream_context_create(array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type: application/json',
            'content' => json_encode($params)
        )
    )));

    return $res;
}

   //发起代付
   public function payment($params) {
      $order_no = $params['order_no'];
      $params = array(
        "mer_no"=>  $this->mer_no,
        "order_no"=> $order_no,
        "method"=> "fund.apply",
        "order_amount"=> $params['order_amount'],
        "currency" => "VND",
        "acc_code" => $params['acc_code'],
        "acc_name" => $params['acc_name'],
        "acc_no" => $params['acc_no'],
        "returnurl" => $this->http_url('playment_return_url')
    );

    $params['sign'] = $this->sign($params);
    $res = file_get_contents($this->mer_pay_url, false, stream_context_create(array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type: application/json',
            'content' => json_encode($params)
        )
    )));



    $array = json_decode($res, true);


    //代付请求失败
    if($array['status']=='fail'){

        $order = $order_no;
        //更新订单状态
        UsersWithdraw::where('order_no',$order)->update(['play_status' => -1,'sys_no'=>$array['status_mes'] ]);

        PlayCommon::updata_user_pric($order);

    }

    return $res;
  }



   //查询代收 $ daifu
   public function query($request,$type) {
        $params = array(
           "mer_no" => $this->mer_no,
           "method" => $type,
           "order_no" => $request->input('order_no'),
        );
        $path = "./logs";
		if(!file_exists($path))
		{
			//检查是否有该文件夹，如果没有就创建，并给予最高权限
			mkdir("$path" , 0700);
		}
		$myfile = fopen("{$path}/log1.txt", "a");
		fwrite($myfile, json_encode($request->all()));
		fclose($myfile);
		$params = $request->all();
		$sign = $params['sign'];
		unset($params['sign']);
		$makesign = $this->sign($params);
		if($sign == $makesign){

            if( $request->input('result') =="success"){
                return true;
            }
            else{
                return false;
            }



		}
		return false;

    //   $params['sign'] = $this->sign($params);
    //   $res = file_get_contents($this->mer_pay_url, false, stream_context_create(array(
    //       'http' => array(
    //           'method' => 'POST',
    //           'header' => 'Content-type: application/json',
    //           'content' => json_encode($params)
    //       )
    //   )));

    //   return $res;
   }

   //构建本地订单

   public function order_create($request,$order){
        $data = [
            'order_no' => $order,
            'type' => $request->input('paytypecode'),
            'price' =>  $request->input('amount'),
            'uname' =>  $request->input('uname'),
            'action_user' =>  $request->input('uname'),
            'uid' =>  $request->input('uid'),
            'amount' =>  $request->input('user_amount'),
            'action' => 5,
            'status' => 0, //待生效
             'num'    => '待支付',
        ];

        UsersRecharge::create($data);

        $online = [
            'order_no' => $order,
            "payemail" =>  $request->input('payemail') ?? '<EMAIL>',
            "payname"  =>  $request->input('payname') ?? 'shana',
            "payphone" =>  $request->input('payphone') ?? '1324673',
            "paytypecode" => $request->input('paytypecode'),
        ];

        $id  =  DB::table('users_recharge_online')->insert($online);
   }



}
