<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Models\UsersRecharge;
use Illuminate\Support\Facades\DB;
use App\Models\UsersWithdraw;
use App\Models\User;
use App\Http\Controllers\PlayCommon;
class PlayKbpay extends Controller
{


  
      // 商户号
   private $mer_no = '6061779';

   // 商户代收密钥
   private $mer_key = 'JjORY2tgcj3kLtWcAm9sJ2mhyHs4eXG2';
   // 商户代付密钥
   private $payout_keys ="TU1QR2L5dSsO59MaztyPnC39joo2PXfk";  

   // 代收测试请求地址
   //private $mer_pay_url = "https://api.kbpay.io/sandbox/payin/submit";

   //代收正式
  private $mer_pay_url = "https://api.kbpay.io/payin/submit";

   //代付测试请求地址
 // private $mer_outpay_url =  "https://api.kbpay.io/sandbox/payout/submit";

   //代付正式
   private $mer_outpay_url = "https://api.kbpay.io/payout/submit";
                                   
   //代收 通知结果地址
    private $mer_return_url = 'https://admin.zaiwejqhrjz.com/api/playback/kbio';

    //代付 通知结果地址
    private $playment_return_url = 'https://admin.zaiwejqhrjz.com/api/playmentback/kbio';



    //定义接口地址
    function http_url($action){

        if($action=='mer_return_url'){ //代收通知

            return env('APP_URL').'/api/playback/kbio';

        }

        else{ //代付通知
            return env('APP_URL').'/api/playmentback/kbio';
        }
        

    }
   //定义curl的post请求方法
    function post_form($url, $data) {
        //初使化init方法
        $ch = curl_init();
        //指定URL
        curl_setopt($ch, CURLOPT_URL, $url);
        //设定请求后返回结果
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //声明使用POST方式来进行发送
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        //发送的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        //忽略证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        //不需要响应的header头信息
        curl_setopt($ch, CURLOPT_HEADER, false);
        //设置请求的header头信息
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
               "Content-Type: application/x-www-form-urlencoded",   
        ));
        //设置超时时间
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        //发送请求
        $object = new ressong();
        $object->response = curl_exec ( $ch );
        $object->info = curl_getinfo ( $ch );
        $object->error_code = curl_errno ( $ch );
        $object->error = curl_error ( $ch );
        //关闭curl
        curl_close($ch);
        //返回数据
        //$data = json_decode($object->response,true);
        return $object->response;
    }


    public function test($request){

        return $this->http_url('mer_return_url');
    }
    public function pay($request){
        //代收密钥
        $order = date('Ymd').time();
        $payin_key =$this->mer_key;  
        $data["merchant_no"]=$this->mer_no;  //商户号
        $data["order_no"]=  $order ;         //订单号
        $data["order_amount"]= sprintf("%.2f", $request->input('amount'));// 金额（float）
        $data["notify_url"]=$this->http_url('mer_return_url');
        $data["timestamp"]= time();  
        $data["payin_method"] =1119; //$request->input('paytypecode');
        //排序
        ksort($data);
        //拼接字符串
        $unescape = urldecode(http_build_query($data));
        $sign_str = $unescape . "&key=" . $payin_key;
        //签名
        $data["sign"] = md5($sign_str);
        //发送post的form请求
        $url = $this->mer_pay_url;  //请求代收地址
        $rs = $this->post_form($url, $data);

        $this->order_create($request,$order);


        $path = "./logs";
        if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/kbio_pay.txt", "a");
		fwrite($myfile,$rs);
		fclose($myfile); 

        return $rs;
    }

    public function query($order_no){

        //代收密钥
        $payin_key =$this->mer_key;  
        $data["merchant_no"]=$this->mer_no;  //商户号
        $data["order_no"]= $order_no;       //订单号
        $data["timestamp"]= time();  
        //排序
        ksort($data);
        //拼接字符串
        $unescape = urldecode(http_build_query($data));
        $sign_str = $unescape . "&key=" . $payin_key;
        //签名
        $data["sign"] = md5($sign_str);
        //发送post的form请求
        $url = $this->mer_pay_url;  //请求代付地址
        $rs = $this->post_form($url, $data);
        return $rs;

    }
    
    public function payment_query($request){
        $path = "./logs";
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log2.txt", "a");
		fwrite($myfile, json_encode($request->all()));
		fclose($myfile); 
		$params = $request->all();
		$sign = $params['sign'];
		unset($params['sign']);
		ksort($params);
		$payout_key            = $this->payout_keys;  
        //拼接字符串
        $unescape = urldecode(http_build_query($params));
        $sign_str = $unescape . "&key=" . $payout_key;
        //签名
        $makesign = md5($sign_str);
		if($sign == $makesign){

           if($request->input('trade_status')==2){
                  return true;
           }
           else{
                  return false;
           }
		   
		}
		return false;
    }


    public function payment($param){

        //代付密钥
        $payout_key            = $this->payout_keys;  
        $data["merchant_no"]   = $this->mer_no;
        $data["order_no"]      = $param['order_no'];
        $data["order_amount"]  = sprintf("%.2f", $param['order_amount']); //金额（float）
        $data["notify_url"]    = $this->http_url('playment_return_url');
        $data["payout_method"] = $param['payout_method'];     //代付编码
        $data["account_no"]    = $param['acc_no'];  //卡号
        $data["account_name"]  = $param['acc_name'];  //姓名
        $data["timestamp"]     = time();


        //排序
        ksort($data);
        //拼接字符串
        $unescape = urldecode(http_build_query($data));
        $sign_str = $unescape . "&key=" . $payout_key;
        //签名
        $data["sign"] = md5($sign_str);
        //发送post的form请求
        $url = $this->mer_outpay_url;
        $rs = $this->post_form($url, $data);



        $array = json_decode($rs, true);
      

        //代付请求失败
        if($array['code']=='1000'){


            $order = $param['order_no'];
            //更新订单状态
            UsersWithdraw::where('order_no',$order)->update(['play_status' => -1,'sys_no'=>$array['message'] ]);

            PlayCommon::updata_user_pric($order);

        }
       

        
        return $rs;
    }

     //构建本地订单

   public function order_create($request,$order){
        $data = [
            'order_no' => $order,
            'type' => $request->input('paytypecode'),
            'price' =>  $request->input('amount'),
            'uname' =>  $request->input('uname'),
            'action_user' =>  $request->input('uname'),
            'uid' =>  $request->input('uid'),
            'amount' =>  $request->input('user_amount'),
            'action' => 6,
            'status' => 0, //待生效
             'num'    => '待支付',
        ];
        
        UsersRecharge::create($data);

        $online = [
            'order_no' => $order,
            "payemail" =>  $request->input('payemail') ?? '<EMAIL>',
            "payname"  =>  $request->input('payname') ?? 'shana',
            "payphone" =>  $request->input('payphone') ?? '1324673',
            "paytypecode" => $request->input('paytypecode'),
        ];

        $id  =  DB::table('users_recharge_online')->insert($online);
    }
 
}


class ressong {


    
}