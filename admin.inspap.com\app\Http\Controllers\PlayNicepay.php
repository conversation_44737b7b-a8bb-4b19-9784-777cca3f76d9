<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use App\Models\UsersRecharge;
use Illuminate\Support\Facades\DB;
use App\Models\UsersWithdraw;
use App\Http\Controllers\PlayCommon;
class PlayNicepay extends Controller
{

   // 商户号
   private $mer_no = 'MCH10213';

   // 商户密钥
   private $mer_key = 'bcd91ec918db706c53d0fc865a7176cb';

   // 请求地址
   private $mer_pay_url = 'http://merchant.nicepay.pro';


  // 代收 返回地址  
    private $mer_return_url = 'https://admin.zaiwejqhrjz.com/api/playback/nicepay';

  //代付 返回地址
  private $playment_return_url = 'https://admin.zaiwejqhrjz.com/api/playmentback/nicepay';


     //定义接口地址
     function http_url($action){

        if($action=='mer_return_url'){ //代收通知

            return env('APP_URL').'/api/playback/nicepay';

        }

        else{ //代付通知
            return env('APP_URL').'/api/playmentback/nicepay';
        }
        

    }

   // 对参数签名
   private function sign($param,$salt) {
    $data = $param;
    ksort($data);

    $str="";
    foreach ($data as $key => $value)
    {
        $str=$str.$value;
    }
    $str = $str.$salt;
    return md5($str);
  }


//发起代收
public function pay($request) {
 
    $order = date('Ymd',time()).time();
    $params = array(

        'app_key'=> $this->mer_no,
        'balance'=> $request->input('amount'),//支付金额，元
        'notify_url'=> $this->http_url('mer_return_url'),//回调地址
        'ord_id'=>$order,//商户自己的订单号

    );

    $this->order_create($request,$order);
    $params['sign'] = $this->sign($params,$this->mer_key);
    $res = file_get_contents($this->mer_pay_url."/api/recharge", false, stream_context_create(array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type: application/json',
            'content' => json_encode($params)
        )
    )));
    $array = json_decode($res, true);
    $re = [];

    //重组数据结构
    if($array['err']==0){
       $re = [
            'message' => 'success',
            'data'    => [
                   'url' => $array['url']
             ]
          ];
    }
    
    return $re;
}

   //发起代付
   public function payment($request) {

        $order_no = $request['order_no'];
        $params = array(
            'app_key'=>$this->mer_no,
            'balance'=>$request['order_amount'],
            'card'=>  $request['acc_no'] , //银行卡号
            'name'=>  $request['acc_name'] , //持卡人
            'p_bank_code'=>  $request['acc_code'] , //银行
            'ord_id'=> $order_no,
            'notify_url'=> $this->http_url('playment_return_url')
        );
        ksort($params);
        $params['sign'] = $this->sign($params,$this->mer_key);

        $res = file_get_contents($this->mer_pay_url."/api/withdraw", false, stream_context_create(array(
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type: application/json',
                'content' => json_encode($params)
            )
        )));

        $array = json_decode($res, true);


        //代付请求失败
        if($array['err']!=0){

            $order = $order_no;
            //更新订单状态
            UsersWithdraw::where('order_no',$order)->update(['play_status' => -1,'sys_no'=>$array['err_msg'] ]);

            PlayCommon::updata_user_pric($order);

        }

        $path = "./logs";
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log_nicepay_daifu_cate.txt", "a");
		fwrite($myfile, $res.$request);
		fclose($myfile); 
    
        return $res;
  }



   //这里其实是代收,代付回调检测
   public function query($request) {


        $params = array(
            'app_key'=> $this->mer_no,
            'ord_id'=>'test71046996',//商户自己的订单号
        );


        $path = "./logs";
		if(!file_exists($path)) 
		{ 
			//检查是否有该文件夹，如果没有就创建，并给予最高权限 
			mkdir("$path" , 0700); 
		}
		$myfile = fopen("{$path}/log_nicepay.txt", "a");
		fwrite($myfile, json_encode($request->all()));
		fclose($myfile); 
		$params = $request->all();
		$sign = $params['sign'];
		unset($params['sign']);
		$makesign = $this->sign($params,$this->mer_key);  //md5签名
		if($sign == $makesign){
           
            if( $request->input('status') ==1){
                return true;
            }
            else{
                return false;
            }
		   

		}
		return false;

   }

   //构建本地订单

   public function order_create($request,$order){
        $data = [
            'order_no' => $order,
            'type' => $request->input('paytypecode'),
            'price' =>  $request->input('amount'),
            'uname' =>  $request->input('uname'),
            'action_user' =>  $request->input('uname'),
            'uid' =>  $request->input('uid'),
            'amount' =>  $request->input('user_amount'),
            'action' => Tools::onlineType('nicepay'),
            'status' => 0, //待生效
             'num'    => '待支付',
        ];
        
        UsersRecharge::create($data);

        $online = [
            'order_no' => $order,
            "payemail" =>  $request->input('payemail') ?? '<EMAIL>',
            "payname"  =>  $request->input('payname') ?? 'shana',
            "payphone" =>  $request->input('payphone') ?? '1324673',
            "paytypecode" => $request->input('paytypecode'),
        ];

        $id  =  DB::table('users_recharge_online')->insert($online);
   }

   

}
