<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;

class Tools extends Controller
{

    static function Returnajax($data = null,$msg = null, $status = 200){

        $json = array(
            'data'   => $data,
            'status' => $status,
            'msg'    => $msg
        );

        return response()->json($json);
    }

	/*新增*/
	static function insert_sql($data,$table){
    
		$orderdata = $data;
		
		$id  =  DB::table($table)->insert($orderdata);
				
		if($id):
				return true;
		else:
				return false;
		endif;
				
	}

	/*修改*/

	static function change_sql($table,$where,$update){
		

		$data  =  DB::table($table)->where($where)->update($update);
        
        if($data):
                return true;
        else:
                return false;
        endif;
		
    }
    
    	/*删除*/

	static function delete_sql($table,$where){
		

		$data  =  DB::table($table)->where($where)->delete();
        
        if($data): 
                return true;
        else:     
                return false;
        endif;
		
	}


    //返回两日期天数差 差值包含当天
    static function ReturnDayDiff($date1,$date2){
        // 指定两个日期，转换为 Unix 时间戳

        $date1 = strtotime($date1); 

        $date2 = strtotime($date2); 

        //计算两个日期之间的时间差

        $diff= $date2 - $date1;
        if($diff<0){
             $days = -1;
        }else{
             $days =abs(round($diff / 86400));
        }
        return $days;
    }

    static function getrandstr($length=6){
        $str='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890';
        $randStr = str_shuffle($str);//打乱字符串
        $rands= substr($randStr,0,$length);//substr(string,start,length);返回字符串的一部分
        return $rands;
    }


    //充值方式
    static function onlineType($name){ 

        $type =[

            '后台充值'  => 1,
            '彩金'      => 2,
            'fastpay'  => 5,
            'kbio'     => 6,
            'nicepay'  => 7,
            'allpay'   => 8,
        ];


        return $type[$name];
    
    }


    static function actionType(){ //操作方式
      

        $lan  = $_GET['language'] ?? strtoupper(app()->getLocale());



        $type =[
           
            'ZH_CN' => [
                '1' => '后台充值',
                '2' => '彩金',
                '3' => '提现',
                '4' => '下单',
                '5' => 'Fastpay充值',
                '6' => 'KBIO充值',
                '7' => 'PayNice充值',
                '8' => 'AllPay充值',
            ],
            'CN' => [
                '1' => '后台充值',
                '2' => '彩金',
                '3' => '提现',
                '4' => '下单',
                '5' => 'Fastpay充值',
                '6' => 'KBIO充值',
                '7' => 'PayNice充值',
                '8' => 'AllPay充值',
            ],

            'TW' => [
                '1' => '後臺充值',
                '2' => '彩金',
                '3' => '提現',
                '4' => '下單',
                '5' => 'Fastpay充值',
                '6' => 'KBIO充值',
                '7' => 'PayNice充值',
                '8' => 'AllPay充值',
            ],
            'VN' =>[
                '1' => 'nạp tiền nền',
                '2' => 'quà tặng',
                '3' => 'rút',
                '4' => 'đặt hàng',
                '5' => 'nạp tiền trực tuyến Fastpay',
                '6' => 'nạp tiền trực tuyến KBIO',
                '7' => 'nạp tiền trực tuyến PayNice',
                '8' => 'nạp tiền trực tuyến充值',
            ],

        ];



        return $type[$lan];
    }

    static function language(){ //多语言
      
        $type = [
            ''   => 'CN',
            'CN' => 'CN',
            'EN' => 'EN',
            'TW' => 'TW',
            'VN' => 'VN',
            'IDN' => 'IDN',
            'PT' => 'PT',
            'ES' => 'ES',
        ];

        return $type;
    }

    static function product_language(){ //产品多语言
      
        $type = [
           
            'CN' => '',
            'EN' => '',
            'VN' => '',
            'IDN' => '',
            'TW' => '',
            'PT' => '',
            'ES' => '',
        ];

        return $type;
    }
    static function productType(){ //注单方式
      
        $lan  = $_GET['language'] ?? strtoupper(app()->getLocale());

        $type = [

            'CN' => [
                '1' => 'Like',  //小
                '2' => 'Support',   //大
                '3' => 'Popularity',     //单
                '4' => 'Ranking',     //双
            ],
            'ZH_CN' => [
                '1' => 'Like',  //小
                '2' => 'Support',   //大
                '3' => 'Popularity',     //单
                '4' => 'Ranking',     //双
            ],
            'VN' => [
                '1' => 'Sản phẩm yêu thích',  //小
                '2' => 'Thống nhất',   //大
                '3' => 'Đơn/Cái',     //单
                '4' => 'Cặp/Cái',     //双
            ],
        ];

        return $type[$lan];
    }

    static function productStatus(){ //产品开奖状态

        $lan  = $_GET['language'] ?? strtoupper(app()->getLocale());


        $type = [
            'CN' => [
                '1' => '等待开奖',
                '2' => '中',
                '-1' => '未中',
            ],
            'ZH_CN' => [
                '1' => '等待开奖',
                '2' => '中',
                '-1' => '未中',
            ],
            'VN' => [
                '1' => 'chờ xổ số',
                '2' => 'trúng số',
                '-1' => 'không thắng',
            ],
        ];

        return $type[$lan];
    }

    static function staffCode($aumt,$type){ //type=1 传递员工ID返回 邀请码 =2 传递邀请码 返回员工信息
        
        
        if($type==1){
            $uc  = DB::table('staff_code as code')
            ->join('admin_users as auser','code.uid','=','auser.id')
            ->select('code.*')
            ->where('auser.id',$aumt)
            ->get();
    
            if($uc){
                return $uc[0]->code;
            }else{
                return false;
            }
       
            
        }


        if($type==2){
            $uc  = DB::table('staff_code as code')
            ->join('admin_users as auser','code.uid','=','auser.id')
            ->select('auser.*')
            ->where('code.code',$aumt)
            ->first();
    
            if($uc){
                return $uc;
            }else{
                return false;
            }
        }

    }

    static function viewroles($grid,$type=1){ //列表查看权限筛选


        if($type==1){
      
            /**权限筛选 */
            $admin   = Auth::guard('admin')->user(); 
            
            $roles = Admin::user()->roles; //获取权限分组
            if($roles[0]->slug == 'staff'){   //如果是员工角色 只看自己所属的用户
                $grid->model()->where('staff_id',$admin->id);

            }

            if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团
                $grid->model()->where('team_id',$admin->team_id);

            }
            
            /**权限筛选 END*/
        }
        if($type==2){
          
        

        }
        
    }

    static function Toolsroles($actions){ //列表查看权限筛选


       
            $admin   = Auth::guard('admin')->user(); 
            
            $roles = Admin::user()->roles; //获取权限分组
            if($roles[0]->slug == 'staff'){   
               $actions->disableDelete();
               $actions->disableEdit();  //禁用普通编辑 
               $actions->QuickEdit();    // 启用快速编辑（弹窗）
               $actions->disableView();

            }

            if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团
               // $actions->disableDelete();
               $actions->disableEdit();  //禁用普通编辑 
               $actions->QuickEdit();    // 启用快速编辑（弹窗）
               $actions->disableView();

            }
            
            if($roles[0]->slug == 'administrator'){   
                // $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();
 
             }

    }


    static function Countroles($mthod,$id){ //获取统计权限筛选

           
            $admin = Auth::guard('admin')->user(); 
            $roles = Admin::user()->roles; //获取权限分组



            if( $mthod=='index'){

                if($roles[0]->slug == 'staff'){   //如果是员工角色 只看自己所属的用户

                    $where = [
                        ['user.staff_id',$admin->id]
                    ];

                }

                if($roles[0]->slug == 'team'){   //如果是团长角色 只看自己所属的团

                    $where = [
                        ['user.team_id',$admin->team_id]
                    ];

                }

                if($roles[0]->slug == 'administrator'){   //如果是团长角色 只看自己所属的团

                    $where = []; 

                }
            }

            if( $mthod=='staff'){
                $where = [
                    ['user.staff_id',$id]
                ];

            }


            if( $mthod=='team'){
                $where = [
                    ['user.team_id',$id]
                ];

            }



            return $where;
    
    }

    


    //返回公共查询日期 默认从本月第一天开始
    static function get_date_to_date(Request $request){
        $todays = date('Y-m-d');
        $datearry = explode('-',$todays);
        $month = $datearry[1];
        $year =  $datearry[0];
        $days =  $datearry[2];
        $now  =  $year."-".$month."-".$days." 23:59:59";   //这里有坑 会查询 到今天最后一秒为止


        $sdtime = [
            'start_at' => ($request->input('start_at')=='') ? $year."-".$month."-".$days : $request->input('start_at'),
            'end_at'   => ($request->input('end_at')=='') ? $now : $request->input('end_at')." 23:59:59" //传递过来的参数如 Y-m-d  就是只查询到 Y-m-d 00:00:00所以加上了 23：59：59
        ];

        return $sdtime;
    }

    //返回全局配置 HTTP

    static function http(){


      return 'https://admin.ffrkjhqwiw.com/';
    }

}
