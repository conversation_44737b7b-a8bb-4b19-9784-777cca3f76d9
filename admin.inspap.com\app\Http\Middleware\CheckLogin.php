<?php

namespace App\Http\Middleware;

use Closure;

class CheckLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
		$dbug = false;
		
		if($dbug){
			//开启了dbug
			
		}else{
			if (!$request->cookie('laravel_uid')) {

                $json = array(
                    'data'   => [],
                    'status' => '20021',
                    'msg'    => '请登录',
                );
				return response()->json($json);
            }

		}
        return $next($request);
    }
	
}
