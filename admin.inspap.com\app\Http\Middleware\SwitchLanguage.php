<?php


# 代码如下
namespace App\Http\Middleware;

use Closure;
use Session;
use App;

class SwitchLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        if (Session::has('locale') && in_array(Session::get('locale'), ['vn','zh_CN', 'en', 'idn', 'tw', 'es', 'pt'])) {
            App::setLocale(Session::get('locale'));
        } else {
            App::setLocale('zh_CN');
        }
        return $next($request);
    }

}