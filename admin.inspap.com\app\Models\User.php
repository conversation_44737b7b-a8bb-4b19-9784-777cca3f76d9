<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class User extends Model
{
	use HasDateTimeFormatter;   
	protected $table = 'users';
    protected $guarded = [];
	public function Adminuser()
    {
		
       return $this->hasOne(Adminuser::class);
    }


    
    public function UsersRecharge()  //设置关联 充值表
    {
        return $this->hasOne(UsersRecharge::class,'uid','uid');
    }



}
