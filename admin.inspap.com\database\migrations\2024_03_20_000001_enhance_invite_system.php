<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnhanceInviteSystem extends Migration
{
    public function up()
    {
        // 增强staff_code表
        Schema::table('staff_code', function (Blueprint $table) {
            if (!Schema::hasColumn('staff_code', 'used_count')) {
                $table->integer('used_count')->default(0)->comment('已使用次数');
            }
            if (!Schema::hasColumn('staff_code', 'max_uses')) {
                $table->integer('max_uses')->default(100)->comment('最大使用次数');
            }
            if (!Schema::hasColumn('staff_code', 'expire_at')) {
                $table->timestamp('expire_at')->nullable()->comment('过期时间');
            }
        });

        // 增强invite_records表
        Schema::table('invite_records', function (Blueprint $table) {
            if (!Schema::hasColumn('invite_records', 'invite_ip')) {
                $table->string('invite_ip')->nullable()->comment('注册IP');
            }
        });

        // 创建邀请码使用日志表
        Schema::create('invite_code_logs', function (Blueprint $table) {
            $table->id();
            $table->string('code')->comment('邀请码');
            $table->unsignedBigInteger('admin_id')->comment('管理员ID');
            $table->string('action')->comment('操作类型');
            $table->text('changes')->nullable()->comment('变更内容');
            $table->timestamps();

            $table->index('code');
            $table->index('admin_id');
        });

        // 创建邀请统计表
        Schema::create('invite_statistics', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('邀请码');
            $table->integer('total_invites')->default(0)->comment('总邀请数');
            $table->integer('today_invites')->default(0)->comment('今日邀请数');
            $table->integer('month_invites')->default(0)->comment('本月邀请数');
            $table->integer('active_users')->default(0)->comment('活跃用户数');
            $table->timestamps();

            $table->index('code');
        });
    }

    public function down()
    {
        // 删除新增的列
        Schema::table('staff_code', function (Blueprint $table) {
            $table->dropColumn(['used_count', 'max_uses', 'expire_at']);
        });

        Schema::table('invite_records', function (Blueprint $table) {
            $table->dropColumn('invite_ip');
        });

        // 删除新建的表
        Schema::dropIfExists('invite_code_logs');
        Schema::dropIfExists('invite_statistics');
    }
} 