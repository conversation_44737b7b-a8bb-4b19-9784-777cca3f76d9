var config_http ='';

Dcat.ready(function () {


	get_withdra_count();

});


//ajax方法
function ajax(url, type, json, callback) {

	var url = config_http + url;

	$.ajax({
		
		url: url,
		type: type,
		dataType: 'json',
		data: json,
		
		xhrFields: {
			withCredentials: true // 这里设置了withCredentials
		},
		success: function(list) {

			if(list.status == 200) {

				callback(list);

			} else if(list.status == 20021) {
				//跳转至登陆
				window.location.href = "/"
			} else {


				//alert(list.msg);
				$('.dcat-admin-body').append(
					` <div id="ajax-container" class="toast-container toast-top-right"><div class="toast toast-error" aria-live="assertive" style="display: block;"><div class="toast-message">${list.msg}</div></div></div>`
				);
			

				$(".dcat-admin-body #ajax-container").fadeOut(5000);
                callback(list);
			
			}

		},
		error: function() {
			//alert('ajax发送失败');
		}
	});
  }

  //设置首页数据

  function setCountdata(json=''){


	
	var url = 'admin/api/getcount';
	
	ajax(url,'get',json,function(data){

		$res = data.data;
		$('.recharge_money').html($res.recharge_money);      
		$('.recharge_pople').html($res.recharge_pople);        
		$('.recharge_first_price').html($res.recharge_first_price);   
		$('.recharge_first_pople').html($res.recharge_first_pople);    
		$('.recharge_number').html($res.recharge_number);         
		$('.withdraw_money').html($res.withdraw_money);         
		$('.withdraw_pople').html($res.withdraw_pople);         
		$('.withdraw_number').html($res.withdraw_number);        
		$('.order_money').html($res.order_money);             
		$('.order_pople').html($res.order_pople);            
		$('.order_number').html($res.order_number);           
		$('.order_lottery').html($res.order_lottery);          
		$('.zhuche_pople').html($res.zhuche_pople);           
		$('.sum_zhuche_pople').html($res.sum_zhuche_pople);       
		$('.sum_recharge_money').html($res.sum_recharge_money);     
		$('.sum_withdraw_money').html($res.sum_withdraw_money);      
		$('.sum_order_money' ).html($res.sum_order_money);        
		$('.sum_order_number').html($res.sum_order_number);       
		$('.sum_order_lottery').html($res.sum_order_lottery);       
	 
	})
  }
  
  //设置员工统计
  function setCountCenterStaff(json=''){
		
	var url = '/admin/api/getcountcenter/staff';
	
	ajax(url,'get',json,function(data){

		var res = data.data;

		var dom = '';
		for(var i=0;i<res.length;i++){

			dom += `  <tr>
			<td>`+res[i].team_name+`</th>
			<td>`+res[i].username+`</th>
			<td>`+res[i].code+`</th>
			<td>`+res[i].popelecount+`</th>
			<td>`+res[i].datacount.zhuche_pople+`</th>
			<td>`+res[i].datacount.recharge_money+`</th>
			<td>`+res[i].datacount.recharge_pople+`</th>
			<td>`+res[i].datacount.recharge_number+`</th>
			<td>`+res[i].datacount.recharge_1_price+`/`+res[i].datacount.recharge_2_price+`/`+res[i].datacount.recharge_3_price+`</th>
			<td>`+res[i].datacount.recharge_1_pople+`/`+res[i].datacount.recharge_2_pople+`/`+res[i].datacount.recharge_3_pople+`</th>
			<td>`+res[i].datacount.withdraw_money+`</th>
			<td>`+res[i].datacount.withdraw_pople+`</th>
			<td>`+res[i].datacount.withdraw_number+`</th>
			<td>`+res[i].datacount.order_money+`</th>
			<td>`+res[i].datacount.order_lottery+`</th>
			<td>`+res[i].datacount.order_pople+`</th>
			</tr>`;
		}

		$('.staff').html(dom);
		

		
	})


  }


  //设置团队统计
  function setCountCenterTeam(json=''){
		
	var url = '/admin/api/getcountcenter/team';
	
	ajax(url,'get',json,function(data){

		var res = data.data;

		var dom = '';
		for(var i=0;i<res.length;i++){

			dom += `  <tr>
			<td>`+res[i].username+`</th>

			<td>`+res[i].team_count+`</th>
			<td>`+res[i].datacount.zhuche_pople+`</th>
			<td>`+res[i].datacount.recharge_money+`</th>
			<td>`+res[i].datacount.recharge_pople+`</th>
			<td>`+res[i].datacount.recharge_number+`</th>
			<td>`+res[i].datacount.recharge_1_price+`</th>
			<td>`+res[i].datacount.recharge_1_pople+`</th>
			<td>`+res[i].datacount.withdraw_money+`</th>
			<td>`+res[i].datacount.withdraw_pople+`</th>
			<td>`+res[i].datacount.withdraw_number+`</th>
			<td>`+res[i].datacount.order_money+`</th>
			<td>`+res[i].datacount.order_lottery+`</th>
			<td>`+res[i].datacount.order_pople+`</th>
			</tr>`;
		}

		$('.staff').html(dom);
		

		
	})
  }

//提现后更新 用户金额 账单

function ajax_user_bill(id){

	var url = '/admin/api/withdrabill/'+id;
	var json = {};
	ajax(url,'get',json,function(data){});

}


//强制下线
function onlineout(uids){

	//$('.out_onlin').bind('click',function(){
                    
		//var uids = $(this).attr('uid');
		var url = '/admin/api/outonline';
		var json = {
			uid:uids
		};
		Dcat.confirm('确认要强制下线账号？', null, function (uids) {
			ajax(url,'get',json,function(data){

				Dcat.reload();

			});
		
		});

	//})
}

//获取提现数量
function get_withdra_count(){

	var url = '/admin/api/withdra/getcount';
	var json = {};
	var i = 0;


	ajax(url,'get',json,function(data){

		 i = data.data;
		 $('.withdrawcount').html(i);
	});
	setInterval(function(){

		ajax(url,'get',json,function(data){
			var n = data.data;
	
			
			if(n>i){
				//播放音源
				
				mp3();
				i++;
			}
			$('.withdrawcount').html(n);
		});
	
	},5000)
	
}

//播放音频

function mp3(){

	var myAudio = new Audio('/upload/files/1.mp3');
     
        myAudio.play();       

}

//语言切换

function change_lang(lang){

	var url = '/admin/changeLocale/'+lang;
	var json = {};
	ajax(url,'get',json,function(data){
		setTimeout(() => {
			//window.location.reload();
		}, 500);
		
	});

}