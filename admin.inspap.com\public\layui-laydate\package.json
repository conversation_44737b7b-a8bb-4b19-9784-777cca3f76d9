{"name": "lay<PERSON>-lay<PERSON>", "realname": "layDate", "version": "5.3.1", "description": "日期与时间组件", "main": "src/laydate.js", "license": "MIT", "scripts": {"run": "gulp"}, "repository": {"type": "https", "url": "git+https://github.com/sentsin/laydate.git"}, "devDependencies": {"gulp": "^3.9.0", "gulp-minify-css": "^1.2.4", "gulp-uglify": "^1.5.4", "gulp-rename": "^1.2.2", "gulp-header": "^1.8.8", "del": "^2.2.2"}, "bugs": {"url": "https://github.com/sentsin/laydate/issues"}, "directories": {"test": "test"}, "dependencies": {}, "keywords": ["laydate", "date", "time", "datetime", "datepicker", "calendar"], "homepage": "https://github.com/sentsin/laydate#readme", "author": ""}