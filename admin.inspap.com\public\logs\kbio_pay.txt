{"code":0,"data":{"merchant_no":"6061779","order_amount":51000,"order_no":"202306171687012644","trade_no":"KBI16870126447554864","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870126447554864"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":15000000,"order_no":"202306181687018667","trade_no":"KBI16870186676544783","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870186676544783"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":788000,"order_no":"202306181687057884","trade_no":"KBI16870578843880304","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870578843880304"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":788000,"order_no":"202306181687057954","trade_no":"KBI16870579546590118","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870579546590118"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":5850000,"order_no":"202306181687064677","trade_no":"KBI16870646776945180","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870646776945180"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":170000,"order_no":"202306181687067032","trade_no":"KBI16870670330819973","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870670330819973"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":215000,"order_no":"202306181687073377","trade_no":"KBI16870733776420225","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870733776420225"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":150000,"order_no":"202306181687073410","trade_no":"KBI16870734112089220","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870734112089220"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":200000,"order_no":"202306181687073454","trade_no":"KBI16870734548005660","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870734548005660"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":200000,"order_no":"202306181687073548","trade_no":"KBI16870735484345532","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870735484345532"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":23400000,"order_no":"202306181687077354","trade_no":"KBI16870773547078141","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870773547078141"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":580000,"order_no":"202306181687079449","trade_no":"KBI16870794498649476","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870794498649476"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":580000,"order_no":"202306181687079455","trade_no":"KBI16870794556141161","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870794556141161"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":200000,"order_no":"202306181687089811","trade_no":"KBI16870898119016264","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870898119016264"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":200000,"order_no":"202306181687089815","trade_no":"KBI16870898158931089","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870898158931089"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":100000,"order_no":"202306181687089825","trade_no":"KBI16870898260796277","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16870898260796277"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":438000,"order_no":"202306191687145505","trade_no":"KBI16871455057644016","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16871455057644016"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":438000,"order_no":"202306191687145605","trade_no":"KBI16871456055018290","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16871456055018290"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":3802500,"order_no":"202306191687149931","trade_no":"KBI16871499315453988","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16871499315453988"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689820207","trade_no":"KBI16898202080220477","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898202080220477"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":4620828,"order_no":"202307201689824287","trade_no":"KBI16898242881208371","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898242881208371"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":10000000,"order_no":"202307201689827895","trade_no":"KBI16898278962878774","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898278962878774"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":10000000,"order_no":"202307201689827998","trade_no":"KBI16898279986745174","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898279986745174"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":4000000,"order_no":"202307201689828138","trade_no":"KBI16898281387671336","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898281387671336"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":23000000,"order_no":"202307201689830521","trade_no":"KBI16898305215365803","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898305215365803"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689832975","trade_no":"KBI16898329753720350","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898329753720350"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":23000000,"order_no":"202307201689841604","trade_no":"KBI16898416048033703","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898416048033703"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":3333333,"order_no":"202307201689847945","trade_no":"KBI16898479459154380","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898479459154380"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":2000000,"order_no":"202307201689853909","trade_no":"KBI16898539095012159","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898539095012159"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689854110","trade_no":"KBI16898541106515116","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898541106515116"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":2000000,"order_no":"202307201689854194","trade_no":"KBI16898541951559922","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898541951559922"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689856782","trade_no":"KBI16898567827431334","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898567827431334"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":10000000,"order_no":"202307201689857096","trade_no":"KBI16898570966192430","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898570966192430"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857857","trade_no":"KBI16898578576694588","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578576694588"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857865","trade_no":"KBI16898578655468551","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578655468551"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857875","trade_no":"KBI16898578760702072","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578760702072"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857881","trade_no":"KBI16898578819327104","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578819327104"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857889","trade_no":"KBI16898578894055355","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578894055355"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857895","trade_no":"KBI16898578957035790","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898578957035790"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307201689857946","trade_no":"KBI16898579470090944","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898579470090944"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":35658949,"order_no":"202307201689857982","trade_no":"KBI16898579827399522","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898579827399522"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":35658949,"order_no":"202307201689858006","trade_no":"KBI16898580069201628","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898580069201628"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":23000000,"order_no":"202307201689863357","trade_no":"KBI16898633582997082","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16898633582997082"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":13862484,"order_no":"202307211689911601","trade_no":"KBI16899116017439552","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16899116017439552"},"message":"success"}{"code":0,"data":{"merchant_no":"6061779","order_amount":2695483,"order_no":"202307211689913740","trade_no":"KBI16899137408837279","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16899137408837279"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":0,"data":{"merchant_no":"6061779","order_amount":1000000,"order_no":"202307211689921123","trade_no":"KBI16899211239815151","url":"https://api.kbpay.io/payin/stage?trade_no=KBI16899211239815151"},"message":"success"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"The transaction amount is lower than the minimum amount of a single transaction"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}{"code":1000,"message":"No trading channels available"}